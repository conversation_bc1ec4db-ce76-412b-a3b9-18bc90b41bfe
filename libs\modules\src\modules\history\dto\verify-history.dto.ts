import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsNotEmpty, IsString } from 'class-validator';
import { AgeFormat } from 'vac-nest-family';
import { VaccineHistoryDetailDto } from 'vac-nest-history';
import { RegimenItem } from 'vac-nest-regimen';
import { ItemScheduleByPerson } from 'vac-nest-schedule';

export class VerifyHistoryDto {
  @ApiProperty()
  @Expose()
  @IsString()
  @IsNotEmpty()
  personId: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsNotEmpty()
  lcvId: string;
}

export class VerifyHistoryRes extends VaccineHistoryDetailDto {
  @ApiProperty({ type: RegimenItem, isArray: true })
  @Expose()
  @Type(() => RegimenItem)
  listSuggestRegimen?: RegimenItem[];

  @ApiProperty({ type: ItemScheduleByPerson, isArray: true })
  @Expose()
  @Type(() => ItemScheduleByPerson)
  listSchedule?: ItemScheduleByPerson[];

  @ApiProperty({ type: RegimenItem, isArray: false })
  @Expose()
  @Type(() => RegimenItem)
  currentRegimen?: RegimenItem;

  @ApiProperty({ type: AgeFormat, isArray: false })
  @Expose()
  @Type(() => AgeFormat)
  customerAge?: AgeFormat;
}
