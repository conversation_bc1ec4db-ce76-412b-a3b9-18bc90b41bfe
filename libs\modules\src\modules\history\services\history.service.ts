import { Injectable } from '@nestjs/common';
import { VacHistoryService } from 'vac-nest-history';
import { SyncHistoryTCQG } from '../../schedules/dto';
import { SchedulesService } from '../../schedules/services/schedules.service';
import { VerifyHistoryDto } from '../dto';
import * as _ from 'lodash';
import { RegimenService } from 'vac-nest-regimen';
import { FamilyService } from 'vac-nest-family';
import { SourceId } from '@libs/modules/schedules/constants';
import { checkRuleAge } from '../functions/regimenRule';

@Injectable()
export class HistoryService {
  constructor(
    private readonly vacHistoryService: VacHistoryService,
    private readonly scheduleService: SchedulesService,
    private readonly regimenCoreService: RegimenService,
    private readonly familyCoreService: FamilyService,
  ) {}

  /*
  Action: nút đồng bộ trên màn hình lịch sử tiêm
  Rule: chỉ đc nhấn cách nhau 10s so với
    1. lần nhấn trước đó 
    2. lần tự động kéo ở màn danh sách lịch sử
  */
  async syncHistoryTCQG(body: SyncHistoryTCQG) {
    try {
      const { data, warning, isSuccess } = await this.scheduleService.syncHistoryFromTcqg(body);
      if (!isSuccess) {
        return {
          isSuccess,
          warning,
        };
      }

      if (!data) {
        return {
          items: [],
          isSuccess: true,
        };
      }

      const rs = await this.scheduleService._shouldOverwriteHistory({
        dataFromHistory: data,
        lcvId: body?.lcvId,
        shouldUpdateInjection: false,
      });

      return {
        ...rs,
        isSuccess: true,
      };
    } catch (error) {
      throw error;
    }
  }

  async verifyHistory(payload: VerifyHistoryDto) {
    const { personId, lcvId } = payload;

    // Nếu lên sau hoặc cùng ticket tối ưu performance thì đổi thành primary person
    const person = await this.familyCoreService.getPersonByLcvId(lcvId);
    const history = await this.vacHistoryService.getByPerson({ personId });
    const historyTCQG = history?.filter((item) => item?.sourceId === SourceId.TCQG);

    const arrRegimen = _.uniq(historyTCQG?.map((item) => item?.regimenId));

    const regimens = await this.regimenCoreService.getRegimenByIds({ regimenIds: arrRegimen });

    const historyTCQConflictRegimen = historyTCQG?.filter((item) => {
      const regimenFind = regimens?.find((regimen) => regimen?.id === item?.regimenId);
      return checkRuleAge(new Date(item?.vaccinatedDate), regimenFind, item?.injection, person?.dateOfBirth);
    });

    const arrSkus = _.uniq(historyTCQConflictRegimen?.map((item) => item?.sku));

    const { data } = await this.regimenCoreService.getRegimenByListSku({ skus: arrSkus });

    historyTCQConflictRegimen?.forEach((item) => {
      const regimenFind = data?.filter((regimen) => regimen?.vaccine?.sku === item?.sku);
      // Danh phác đồ gợi ý
      const listSuggestRegimen = regimenFind?.filter((regimen) => {
        if (item?.regimenId === regimen?.id) return false;
        return !checkRuleAge(new Date(item?.vaccinatedDate), regimen, item?.injection, person?.dateOfBirth);
      });
      item['listSuggestRegimen'] = listSuggestRegimen;
    });

    return historyTCQConflictRegimen;
  }
}
