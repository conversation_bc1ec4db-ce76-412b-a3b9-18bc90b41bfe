import { Injectable } from '@nestjs/common';
import { VacHistoryService } from 'vac-nest-history';
import { SyncHistoryTCQG } from '../../schedules/dto';
import { SchedulesService } from '../../schedules/services/schedules.service';
import { UpdateHistoryDobDto, VerifyHistoryDto, VerifyHistoryRes } from '../dto';
import * as _ from 'lodash';
import { RegimenService } from 'vac-nest-regimen';
import { FamilyService } from 'vac-nest-family';
import { SourceId } from '@libs/modules/schedules/constants';
import { checkRuleAge, sortSuggestRegimenByAge } from '../functions/regimenRule';
import { ScheduleCoreService } from 'vac-nest-schedule';
import { calculateTimeDifference } from 'vac-commons';

@Injectable()
export class HistoryService {
  constructor(
    private readonly vacHistoryService: VacHistoryService,
    private readonly scheduleService: SchedulesService,
    private readonly regimenCoreService: RegimenService,
    private readonly familyCoreService: FamilyService,
    private readonly scheduleCoreService: ScheduleCoreService,
  ) {}

  /*
  Action: nút đồng bộ trên màn hình lịch sử tiêm
  Rule: chỉ đc nhấn cách nhau 10s so với
    1. lần nhấn trước đó 
    2. lần tự động kéo ở màn danh sách lịch sử
  */
  async syncHistoryTCQG(body: SyncHistoryTCQG) {
    try {
      const { data, warning, isSuccess } = await this.scheduleService.syncHistoryFromTcqg(body);
      if (!isSuccess) {
        return {
          isSuccess,
          warning,
        };
      }

      if (!data) {
        return {
          items: [],
          isSuccess: true,
        };
      }

      const rs = await this.scheduleService._shouldOverwriteHistory({
        dataFromHistory: data,
        lcvId: body?.lcvId,
        shouldUpdateInjection: false,
      });

      return {
        ...rs,
        isSuccess: true,
      };
    } catch (error) {
      throw error;
    }
  }

  async verifyHistory(payload: VerifyHistoryDto): Promise<VerifyHistoryRes[]> {
    const { personId, lcvId } = payload;

    // Nếu lên sau hoặc cùng ticket tối ưu performance thì đổi thành primary person
    const person = await this.familyCoreService.getPersonByLcvId(lcvId);
    // Lịch sử
    const history = await this.vacHistoryService.getByPerson({ personId });
    const historyTCQG = history?.filter((item) => item?.sourceId === SourceId.TCQG);
    if (!history?.length) return [];

    // DS phác đồ theo lịch sử
    const arrRegimen = _.uniq(historyTCQG?.map((item) => item?.regimenId));
    const regimens = await this.regimenCoreService.getRegimenByIds({ regimenIds: arrRegimen });

    // Lọc lịch sử theo phác đồ không thỏa rule tuổi
    const historyTCQConflictRegimen = historyTCQG?.filter((item) => {
      const regimenFind = regimens?.find((regimen) => regimen?.id === item?.regimenId);
      return checkRuleAge(new Date(item?.vaccinatedDate), regimenFind, item?.injection, person?.dateOfBirth);
    });
    if (!historyTCQConflictRegimen?.length) return [];
    // Lấy ds phác đồ theo sku
    const arrSkus = _.uniq(historyTCQConflictRegimen?.map((item) => item?.sku));
    const { data } = await this.regimenCoreService.getRegimenByListSku({ skus: arrSkus });
    // Lọc ra các phác đồ khác
    const arrRegimenConflict = _.uniq(historyTCQConflictRegimen?.map((item) => item?.regimenId));
    const arrOtherRegimen = _.uniq(data?.map((item) => item?.id)?.filter((item) => !arrRegimenConflict.includes(item)));

    // Lấy thông tin các phác đồ khác
    const otherRegimens = await this.regimenCoreService.getRegimenByIds({ regimenIds: arrOtherRegimen });

    // Lịch hẹn của KH
    const listSchedule = await this.scheduleCoreService.getScheduleByPersonCodeV2({
      personCode: lcvId,
      skipCount: 0,
      maxResultCount: 200,
      status: [0],
    });

    const ageRanges = await this.regimenCoreService.getAgeRanges();

    // Tìm những phác đồ phù hợp nhất với lịch sử thỏa rule tuổi và xếp theo thứ tự ưu tiên ageUnitCode, from, isDefault, to
    historyTCQConflictRegimen?.forEach((item: VerifyHistoryRes) => {
      const regimenFind = otherRegimens?.filter((regimen) => regimen?.vaccine?.sku === item?.sku);
      // Danh phác đồ gợi ý
      const listSuggestRegimen = regimenFind?.filter((regimen) => {
        if (item?.regimenId === regimen?.id) return false;
        return !checkRuleAge(new Date(item?.vaccinatedDate), regimen, item?.injection, person?.dateOfBirth);
      });
      item.listSuggestRegimen = sortSuggestRegimenByAge(listSuggestRegimen);
      // Lấy ds lịch hẹn theo regimen
      const listScheduleRegimen = listSchedule?.items?.filter((schedule) => schedule?.regimenId === item?.regimenId);
      item.listSchedule = listScheduleRegimen;

      const currentRegimen = regimens?.find((regimen) => regimen?.id === item?.regimenId);
      item.currentRegimen = currentRegimen;

      // tính ngày/tháng/tuổi của person
      const calculateAge = calculateTimeDifference(
        new Date(person?.dateOfBirth),
        person?.from,
        person?.to,
        ageRanges,
        person?.ageUnitCode,
        new Date(item?.vaccinatedDate),
      );
      item.customerAge = calculateAge;
    });

    return historyTCQConflictRegimen;
  }

  async updateHistory(payload: UpdateHistoryDobDto) {
    return true;
  }
}
