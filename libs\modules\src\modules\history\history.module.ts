import { Module } from '@nestjs/common';
import { HistoryController } from './controllers/history.controller';
import { HistoryService } from './services/history.service';
import { SchedulesModule } from '../schedules/schedules.module';
import { VacHistoryModule } from 'vac-nest-history';
import { FamilyModule } from 'vac-nest-family';
import { RegimenModule } from 'vac-nest-regimen';
import { ScheduleCoreModule } from 'vac-nest-schedule';

@Module({
  imports: [SchedulesModule, VacHistoryModule, RegimenModule, FamilyModule, ScheduleCoreModule],
  controllers: [HistoryController],
  providers: [HistoryService],
})
export class HistoryModule {}
