import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNumber, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator';
import { VoucherGenPaymentLinkLibDto } from 'vac-nest-payment-portal';

export class SendSmsPaymentDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  inside?: string;

  @ApiProperty()
  @IsNumber()
  amount: number;

  @ApiProperty()
  @IsString()
  orderCode: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  promotionCode?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  bankCode?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  paymentMethod?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  createBy?: string;

  @ApiProperty()
  @IsOptional()
  @IsObject({ each: true })
  @Type(() => VoucherGenPaymentLinkLibDto)
  @ValidateNested()
  vouchers?: VoucherGenPaymentLinkLibDto[];

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  partnerId?: number;
}

export class SendSmsAlepayInstallmentData {
  @ApiProperty()
  @IsOptional()
  @IsNumber()
  month?: number;
  @ApiProperty()
  @IsOptional()
  @IsString()
  bankCode?: string;
  @ApiProperty()
  @IsOptional()
  @IsString()
  paymentMethod?: string;
  @ApiProperty()
  @IsOptional()
  isInstallment?: boolean; // true tra gop, false ck thuong}
}

export class SendSmsAlepayInstallmentDto extends SendSmsPaymentDto {
  @ApiProperty()
  @ValidateNested()
  @Type(() => SendSmsAlepayInstallmentData)
  installmentData?: SendSmsAlepayInstallmentData;
}
