import { Body, Controller, Get, HttpCode, HttpStatus, Post, Query } from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { generalSchema } from '../../../../../shared/src';
import { GetInjectionSchedule } from 'vac-nest-schedule-engine-app';
import { SyncHistoryTCQG } from '../../schedules/dto';
import { HistoryService } from '../services/history.service';
import { VerifyHistoryDto, VerifyHistoryRes } from '../dto';
import { UpdateHistoryDto } from 'vac-nest-history';
import { UpdateHistoryDobDto } from '../dto/update-history.dto';

@Controller({ path: 'history', version: '1' })
@ApiTags('history')
@ApiBearerAuth('defaultJWT')
@ApiExtraModels(VerifyHistoryRes)
export class HistoryController {
  constructor(private readonly historyService: HistoryService) {}

  @Post('sync-tcqg')
  @ApiOperation({
    summary: 'Đồng bộ lịch sử từ tcqg',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    schema: generalSchema(GetInjectionSchedule, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    // type: ClassErrorResponse,
  })
  syncTqcg(@Body() body: SyncHistoryTCQG) {
    return this.historyService.syncHistoryTCQG(body);
  }

  @Get('verify-history')
  @ApiOperation({
    summary: 'Kiểm tra lịch sử tiêm không phù hợp với độ tuổi',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    schema: generalSchema(VerifyHistoryRes, 'array'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    // type: ClassErrorResponse,
  })
  verifyHistory(@Query() payload: VerifyHistoryDto) {
    return this.historyService.verifyHistory(payload);
  }

  @Post('update-history')
  @ApiOperation({
    summary: 'Cập nhật lịch sử tiêm',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    schema: generalSchema(UpdateHistoryDobDto, 'object'),
  })
  @ApiBadRequestResponse({
    description: 'Trả lỗi khi đầu vào bị sai',
    // type: ClassErrorResponse,
  })
  updateHistory(@Query() payload: UpdateHistoryDobDto) {
    return this.historyService.updateHistory(payload);
  }
}
