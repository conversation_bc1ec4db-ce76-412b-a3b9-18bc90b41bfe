import { RegimenItem, RegimenRulesDto } from 'vac-nest-regimen';
import { RegimenRulesType, TypeUnitFrom, UnitFromCode } from '../constants';
import moment, { unitOfTime } from 'moment';
import { Logger } from '@nestjs/common';

export const DEFAULT_DATE_ADD = 30;
export const DEFAULT_DATE_ADD_WITH_NOT_STOCK = 7;
export const TIMEZONE = '+07:00';
export const FORMAT_DATE = 'YYYY-MM-DD';

export function filterRegimenRuleByType(regimen: RegimenItem, orderInjection: number, type: number) {
  const detailRegimen = regimen?.details?.find((e) => e.order === orderInjection);
  const filterByRegimenRuleType = detailRegimen?.regimenRules?.filter((i) => i?.regimenRulesType === type);
  if (!filterByRegimenRuleType?.length) return null;
  return {
    regimentRules: filterByRegimenRuleType || [],
    ageFromValue: regimen?.ageFromValue,
    ageFromUnitCode: regimen?.ageFromUnitCode,
    equalFromAge: regimen?.equalFromAge,
    ageToValue: regimen?.ageToValue,
    ageToUnitCode: regimen?.ageToUnitCode,
    equalToAge: regimen?.equalToAge,
  };
}

/**
 * @description cộng ngày/tháng/năm theo định nghĩa
 * @param date
 * @param age
 * @param ageUnitCode
 * @returns
 */
export function addDateInAge(date: Date, age: number, ageUnitCode: TypeUnitFrom, equal?: boolean) {
  const newDate = moment(date);
  switch (ageUnitCode) {
    case TypeUnitFrom.DAYS:
      newDate.add(age, 'days');
      break;
    case TypeUnitFrom.WEEK:
      newDate.add(age, 'weeks');
      break;
    case TypeUnitFrom.MONTHS:
      newDate.add(age, 'months');
      break;
    case TypeUnitFrom.AGES:
      newDate.add(age, 'years');
      break;
    default:
      break;
  }
  if (equal === false) {
    newDate.add(1, 'days');
  }
  return new Date(newDate.format());
}

export function calculateTimeDifference(startDate: Date) {
  // startDate = new Date('2023-10-16T00:00:00Z');
  const startMoment = moment(moment(startDate).utcOffset(7).format(FORMAT_DATE)); // Chuyển ngày bắt đầu thành đối tượng Moment
  const currentMoment = moment(moment().utcOffset(7).format(FORMAT_DATE)); // Lấy ngày hiện tại

  const diffInDays = currentMoment.diff(startMoment, 'days'); // Số ngày khác biệt
  const diffInMonths = currentMoment.diff(startMoment, 'months'); // Số tháng khác biệt
  const diffInYears = currentMoment.diff(startMoment, 'years'); // Số năm khác biệt

  // Tính số ngày còn lại sau khi tính số năm, số tháng và số tuần
  const remainingDaysInYears = currentMoment.clone().subtract(diffInYears, 'years').diff(startMoment, 'days');
  const remainingDaysInMonths = currentMoment.clone().subtract(diffInMonths, 'months').diff(startMoment, 'days');
  const remainingDaysInWeeks = currentMoment.clone().subtract(diffInDays, 'days').diff(startMoment, 'days');

  // Trả về kết quả
  return {
    years: diffInYears,
    months: diffInMonths,
    weeks: diffInDays / 7,
    days: diffInDays,
    remainingDaysInYears: remainingDaysInYears,
    remainingDaysInMonths: remainingDaysInMonths,
    remainingDaysInWeeks: remainingDaysInWeeks,
  };
}

/**
 *
 * @param {Date} date format date
 * @param {String} timeZone default +00:00
 * @param {String} format default ""
 * @returns String
 */
export function parseDateTimeZone(date: Date, timeZone: '+00:00' | '+07:00', format: string = ''): string {
  return moment(date).utcOffset(timeZone).format(format);
}

/**
 * @description Tính khoảng cách
 * @param startDate Thời gian quá khứ
 * @param endDate THời gian tương lai
 * @param unit Khoảng cách D | M | Y
 * @returns Số khoảng cách
 */
export function diffDate(startDate: Date | string, endDate: Date | string, unit: unitOfTime.Diff): number {
  const startDateMoment = moment(parseDateTimeZone(new Date(startDate), TIMEZONE, FORMAT_DATE));
  Logger.log(`StartDate: ${startDateMoment.format()}`);
  const endDateMoment = moment(parseDateTimeZone(new Date(endDate), TIMEZONE, FORMAT_DATE));
  Logger.log(`EndDate: ${endDateMoment.format()}`);
  return endDateMoment.diff(startDateMoment, unit);
}

export function isNotOldAge(date: Date, regimen: RegimenItem, orderInjection: number, dateOfBirth: Date): boolean {
  const filterByRuleType = filterRegimenRuleByType(regimen, orderInjection, RegimenRulesType.THIEU_TUOI);
  Logger.log(`[Data rule chưa đủ tuổi]: ${JSON.stringify(filterByRuleType)}`);
  if (!filterByRuleType) return false;

  const { ageFromValue, ageFromUnitCode, equalFromAge, regimentRules } = filterByRuleType;
  Logger.log(`[Regimen Rule có RuleType = 2]: ${JSON.stringify(regimentRules)}`);

  // tính lịch hẹn thấp nhất theo phác đồ chuẩn
  const minDateRegimen = addDateInAge(new Date(dateOfBirth), ageFromValue, ageFromUnitCode, equalFromAge);
  Logger.log(`[Thời gian phác đồ min]: ${moment(minDateRegimen).utcOffset(7).format('YYYY-MM-DD')}`);
  // tính ngày/tháng/tuổi của person
  const calculateAge = calculateTimeDifference(new Date(dateOfBirth));
  Logger.log(`Thông tin KH => Ngày sinh: ${dateOfBirth} - Thông tin tính tuổi: ${JSON.stringify(calculateAge)}`);

  // tính khoảng cách của ngày thấp nhất tới ngày hẹn
  const calculateDistanceDate = diffDate(minDateRegimen, date, 'days');
  Logger.log(
    `[Khoảng cách từ ngày hẹn tới min phác đồ]: ${calculateDistanceDate} - equalFromAge: ${equalFromAge} - minDateRegimen: ${minDateRegimen} - hẹn: ${date}`,
  );
  if (calculateDistanceDate >= 0 && equalFromAge) return false; // equalFromAge === true cho phép bằng với ngày min
  if (calculateDistanceDate > 0 && !equalFromAge) return false; // equalFromAge === false cho phép không bằng với ngày min

  if (!regimentRules?.length) return false;
  for (const item of regimentRules) {
    // tính khoảng cách thêm 1 lần nữa dựa vào unit trong regiment rule
    const calculateDistanceDateByUnitFrom = diffDate(date, minDateRegimen, UnitFromCode[item?.fromUnit]);
    Logger.log(
      `[RegimentRules][Khoảng cách ngày hẹn đến min phác đồ]: ${calculateDistanceDateByUnitFrom} - unit: ${item?.fromUnit} - hẹn: ${date} - min: ${minDateRegimen}`,
    );

    if (calculateDistanceDateByUnitFrom <= 0) return;
    // có from và to khác 0
    // check trong khoàng
    if (item?.from && item?.to) {
      if (
        (item?.equalFrom &&
          item?.equalTo &&
          calculateDistanceDateByUnitFrom >= item?.from &&
          calculateDistanceDateByUnitFrom <= item?.to) ||
        (item?.equalFrom &&
          !item?.equalTo &&
          calculateDistanceDateByUnitFrom >= item?.from &&
          calculateDistanceDateByUnitFrom < item?.to) ||
        (!item?.equalFrom &&
          item?.equalTo &&
          calculateDistanceDateByUnitFrom > item?.from &&
          calculateDistanceDateByUnitFrom <= item?.to) ||
        (!item?.equalFrom &&
          !item?.equalTo &&
          calculateDistanceDateByUnitFrom > item?.from &&
          calculateDistanceDateByUnitFrom < item?.to)
      ) {
        Logger.log(
          `[Rule][Khách hàng chưa đủ tuổi tiêm theo phác đồ thuộc trong khoảng]:, Mũi thứ: ${orderInjection} - Phác đồ: ${regimen?.vaccine?.name} - Khoảng cách: ${calculateDistanceDateByUnitFrom} - Unit: ${item?.fromUnit} - AlertLevel: ${item?.alertLevel}`,
        );
        return true;
      }
    }
    // có from và to là vô cực
    // check lớn hơn
    if (item?.from && !item?.to) {
      if (
        (item?.equalFrom && calculateDistanceDateByUnitFrom >= item?.from) ||
        (!item?.equalFrom && calculateDistanceDateByUnitFrom > item?.from)
      ) {
        return true;
      }
    }
  }
  return false;
}

export function isOverAge(date: Date, regimen: RegimenItem, orderInjection: number, dateOfBirth: Date): boolean {
  const filterByRuleType = filterRegimenRuleByType(regimen, orderInjection, RegimenRulesType.QUA_TUOI);
  Logger.log(`[Data rule quá tuổi]: ${JSON.stringify(filterByRuleType)}`);
  if (!filterByRuleType) return false;

  const { ageToValue, ageToUnitCode, equalToAge, regimentRules } = filterByRuleType;
  Logger.log(`[Regimen Rule có RuleType = 3]: ${JSON.stringify(regimentRules)}`);
  // tính lịch hẹn cao nhất của phác đồ chuẩn
  let maxDateRegimen = addDateInAge(new Date(dateOfBirth), ageToValue, ageToUnitCode);
  if (!equalToAge) {
    maxDateRegimen = new Date(moment(maxDateRegimen).subtract(1, 'days').format());
  }
  Logger.log(`[Thời gian phác đồ max]: ${moment(maxDateRegimen).utcOffset(7).format('YYYY-MM-DD')}`);
  // tính ngày/tháng/tuổi của person

  // tính khoảng cách của ngày thấp nhất tới ngày hẹn
  const calculateDistanceDate = diffDate(date, maxDateRegimen, 'days');
  Logger.log(
    `[Khoảng cách từ ngày hẹn tới max phác đồ]: ${calculateDistanceDate} - equalToAge: ${equalToAge} - maxDateRegimen: ${maxDateRegimen} - hẹn: ${date}`,
  );
  if (calculateDistanceDate >= 0 && equalToAge) return false; // equalFromAge === true cho phép bằng với ngày min
  if (calculateDistanceDate > 0 && !equalToAge) return false; // equalFromAge === false cho phép không bằng với ngày min

  if (!regimentRules?.length) return false;
  for (const item of regimentRules) {
    // tính khoảng cách thêm 1 lần nữa dựa vào unit trong regiment rule
    const calculateDistanceDateByUnitTo = diffDate(maxDateRegimen, date, UnitFromCode[item?.toUnit]);

    Logger.log(
      `[RegimentRules][Khoảng cách ngày hẹn đến max phác đồ]: ${calculateDistanceDateByUnitTo} - unit: ${item?.toUnit} - hẹn: ${date} - min: ${maxDateRegimen}`,
    );
    if (calculateDistanceDateByUnitTo <= 0) return;
    // có from và to khác 0
    // check trong khoàng
    if (item?.from && item?.to) {
      if (
        (item?.equalFrom &&
          item?.equalTo &&
          calculateDistanceDateByUnitTo >= item?.from &&
          calculateDistanceDateByUnitTo <= item?.to) ||
        (item?.equalFrom &&
          !item?.equalTo &&
          calculateDistanceDateByUnitTo >= item?.from &&
          calculateDistanceDateByUnitTo < item?.to) ||
        (!item?.equalFrom &&
          item?.equalTo &&
          calculateDistanceDateByUnitTo > item?.from &&
          calculateDistanceDateByUnitTo <= item?.to) ||
        (!item?.equalFrom &&
          !item?.equalTo &&
          calculateDistanceDateByUnitTo > item?.from &&
          calculateDistanceDateByUnitTo < item?.to)
      ) {
        Logger.log(
          `[Rule][Khách hàng quá tuổi tiêm theo phác đồ thuộc trong khoảng]:, Mũi thứ: ${orderInjection} - Phác đồ: ${regimen?.vaccine?.name} - Khoảng cách: ${calculateDistanceDateByUnitTo} - Unit: ${item?.toUnit} - alertLevel: ${item?.alertLevel}`,
        );
        return true;
      }
    }
    // có from và to là vô cực
    // check lớn hơn
    if (item?.from && !item?.to) {
      if (
        (item?.equalFrom && calculateDistanceDateByUnitTo >= item?.from) ||
        (!item?.equalFrom && calculateDistanceDateByUnitTo > item?.from)
      ) {
        Logger.log(
          `[Rule][Khách hàng quá tuổi tiêm theo phác đồ thuộc ngoài khoảng từ ${item?.from} đến vô cực]:, Mũi thứ: ${orderInjection} - Phác đồ: ${regimen?.vaccine?.name} - Khoảng cách: ${calculateDistanceDateByUnitTo} - Unit: ${item?.toUnit} - alertLevel: ${item?.alertLevel}`,
        );
        return true;
      }
    }
  }
  return false;
}

export function checkRuleAge(date: Date, regimen: RegimenItem, orderInjection, dateOfBirth: Date) {
  // if (!current?.person?.dateOfBirth) return false;
  // rule chặn chưa đủ tuổi
  const isNotOldAgeResult = isNotOldAge(date, regimen, orderInjection, dateOfBirth);
  // rule chặn quá tuổi
  const isOverAgeResult = isOverAge(date, regimen, orderInjection, dateOfBirth);
  if (isNotOldAgeResult) return isNotOldAgeResult;
  if (isOverAgeResult) return isOverAgeResult;
  return false;
}
