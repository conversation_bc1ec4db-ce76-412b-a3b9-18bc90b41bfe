import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional } from 'class-validator';

export class DataRedisIncentiveOrderDto {
  @ApiProperty({ description: 'LCV ID' })
  @Expose()
  @IsOptional()
  lcvId: string;

  @ApiProperty({ description: 'Order code' })
  @Expose()
  @IsOptional()
  orderCode: string;

  @ApiProperty({ description: 'Order codes' })
  @Expose()
  @IsOptional()
  orderCodes: string[];

  @ApiProperty({ description: 'Order attribute' })
  @Expose()
  @IsOptional()
  orderAttribute: number;

  @ApiProperty({ description: 'Order status' })
  @Expose()
  @IsOptional()
  @IsOptional()
  orderStatus: number;

  @ApiProperty({ description: 'Created by user ID' })
  @Expose()
  @IsOptional()
  createdBy: string;

  @ApiProperty({ description: 'Created by user name' })
  @Expose()
  @IsOptional()
  createdByName: string;

  @ApiProperty({ description: 'Shop code' })
  @Expose()
  @IsOptional()
  shopCode: string;

  @ApiProperty({ description: 'Shop name' })
  @Expose()
  @IsOptional()
  shopName: string;

  @ApiProperty({ description: 'Shop code that created the order' })
  @Expose()
  @IsOptional()
  shopCodeCreate: string;

  @ApiProperty({ description: 'Shop name that created the order' })
  @Expose()
  @IsOptional()
  shopNameCreate: string;

  @ApiProperty({ description: 'Channel that created the order' })
  @Expose()
  @IsOptional()
  orderChannel?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  totalPayment?: number;
}

export class SaveIncentiveOrderItemDto {
  @ApiProperty({ description: 'LCV ID' })
  @Expose()
  @IsOptional()
  lcvId: string;

  @ApiProperty({ description: 'Incentive order data' })
  @Expose()
  @IsOptional()
  data: DataRedisIncentiveOrderDto;
}

export class IncentiveOrderRedisResultDto {
  @ApiProperty({ description: 'LCV ID' })
  @Expose()
  @IsOptional()
  lcvId: string;

  @ApiProperty({ description: 'Exists' })
  @Expose()
  @IsOptional()
  orderCode: string;

  @ApiProperty({ description: 'Incentive order data' })
  @Expose()
  @IsOptional()
  data: DataRedisIncentiveOrderDto;

  @ApiProperty({ description: 'Exists' })
  @Expose()
  @IsOptional()
  exists: boolean;
}
