import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { PromotionFamilyPackageDto } from './print-invoice-family-package.dto';
import { DosageItem } from './print-invoice.dto';

export class GetPreviewQuoteFamilyPackageDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  @Expose()
  orderIdInter: string;
}

export class MoneyDataDto {
  @ApiProperty()
  @Expose()
  totalPricePay: number;

  @ApiProperty()
  @Expose()
  voucherPrice: number;

  @ApiProperty()
  @Expose()
  serviceFee: number;
}

export class OrderPFDetailDto {
  @ApiProperty()
  @Expose()
  @IsString()
  lcvId: string;

  @ApiProperty()
  @Expose()
  @IsString()
  customerName: string;

  @ApiProperty()
  @Expose()
  @IsString()
  appointmentDate: string;

  @ApiProperty({ required: false, type: DosageItem })
  @Expose()
  @IsOptional()
  @Type(() => DosageItem)
  dosageItems?: DosageItem[];
}

class OrderSummaryDataDto {
  @ApiProperty()
  @Expose()
  @IsString()
  customerBuyerName: string;

  @ApiProperty()
  @Expose()
  @IsString()
  customerBuyerPhone: string;

  @ApiProperty()
  @Expose()
  @IsString()
  shopName: string;

  @ApiProperty()
  @Expose()
  @IsString()
  shopCode: string;

  @ApiProperty()
  @Expose()
  @IsString()
  shopAddress: string;

  @ApiProperty()
  @Expose()
  @IsString()
  displayName: string;

  @ApiProperty()
  @Expose()
  @IsString()
  appointmentDate: string;

  @ApiProperty()
  @Expose()
  @IsNumber()
  friendSell: number;
}

export class PreviewQuoteFamilyPackageRes {
  @ApiProperty({ type: OrderSummaryDataDto })
  @Expose()
  @Type(() => OrderSummaryDataDto)
  orderSummaryData: OrderSummaryDataDto;

  @ApiProperty({ type: [PromotionFamilyPackageDto] })
  @Expose()
  @IsArray()
  @Type(() => PromotionFamilyPackageDto)
  promotions: PromotionFamilyPackageDto[];

  @ApiProperty({ type: [OrderPFDetailDto] })
  @Expose()
  @IsArray()
  @Type(() => OrderPFDetailDto)
  orderDetails: OrderPFDetailDto[];

  @ApiProperty({ type: MoneyDataDto })
  @Expose()
  @Type(() => MoneyDataDto)
  moneyData: MoneyDataDto;
}
