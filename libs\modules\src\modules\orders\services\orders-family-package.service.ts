import { CustomersRuleService } from '@libs/modules/customers/services/customers-rule.service';
import { RegimensService } from '@libs/modules/regimens/services/regimens.service';
import { HttpStatus, Inject, Injectable, LoggerService, Logger } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import {
  concurrentPromise,
  ErrorCode,
  getExpiredTime,
  IError,
  isSameDate,
  ITEM_VACCINE_SO1,
  parseJson,
  RedisService,
  SystemException,
  TICKET_CREATE_KEY,
  OrderChannels,
  getStartDate,
  EnmCartType,
  OrderChanel,
} from '@shared';
import { Request } from 'express';
import { JSONPath } from 'jsonpath-plus';
import _ from 'lodash';
import moment from 'moment';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import {
  AdjustOrderLibResponse,
  CartAppService,
  GetCartLibResponse,
  ItemType,
  MergeCartToCustomerLibDto,
  ListCartSelectedLib,
  GenericSessionLibResponse,
} from 'vac-nest-cart-app';
import {
  AssignRoomDtoV2,
  ClinicType,
  CreateTicketDto,
  CreateTicketRes,
  EnmAssignRule,
  EnmScheduleStatus,
  EnmStatusTicket,
  EnmTicketType,
  ExaminationCoreService,
  PaymentType,
} from 'vac-nest-examination';
import { FamilyService } from 'vac-nest-family';
import {
  BaseJourneyRes,
  IOrder,
  JourneyService,
  MultipleJourneyDto,
  STEP_NAME,
  TRANSACTION_TYPE,
  TypeStepId,
} from 'vac-nest-journey';
import {
  CreateOrderRes,
  DetailAttachment,
  EcomDisplay,
  EmployeeStep,
  GetOneOrderLibResponse,
  OMSService,
  OrderProperty,
  OrderStatus,
  PayloadUpdatedStatusOrderDto,
  SearchOrderESLibResponse,
  transformPlaceOrder,
  UpdateOrderStatusFamilyPackageDto,
} from 'vac-nest-oms';
import {
  CreateAdjustPaymentRes,
  getDepositDetailAmountRealAmount,
  getDepositedAmountByMethods,
  GetPaymentHistoryESLibResponse,
  PaymentGatewayService,
  PaymentOnlineStatus,
  PaymentSourceType,
  TypePayment,
} from 'vac-nest-payment-gateway';
import { GetOneProductBySkuRes, PIMAppService } from 'vac-nest-pim-app';
import { CheckPromotionResponse, PromotionResponse, PromotionService } from 'vac-nest-promotion';
import { RegimenService } from 'vac-nest-regimen';
import {
  CancelOrderDto,
  EmployeeInfoContinueBuyingDto,
  PlaceOrderDto,
  SearchOrderDynamicDto,
  UpdateStatusOrderManyDto,
} from '../dto';
import { BusinessOrderService } from './business-order.services';
import { OrderUtilsService } from './order-utils.services';
import { FLOW_JOURNEY_ENUM, ORDER_ATTRIBUTE, PartialOrderPaymentCreate } from '../constants';
import { OrdersService } from './orders.service';
import { FilesService } from '@libs/modules/files/services/files.service';
import { calculateTimeDifference } from 'vac-commons';
import { CustomersService } from '@libs/modules/customers/services/customers.service';
import { VerifyVoucherResponse } from 'vac-nest-voucher-core';
import { plainToInstance } from 'class-transformer';
import { ListGiftCreateOrderTransformDto } from '../dto/list-gift-create-order-family-package-transform.dto';
import { CheckRuleMaxInjectionDto, OrderRuleEngineService } from 'vac-nest-order-rule-engine';
import { OrderRedisService } from './order-redis.service';
import { CamundaApiService, CancelDepositDto } from '@frt/nestjs-api';
import { IncentiveOrderRedisResultDto } from '../dto/redis-incentive-order.dto';
import { CustomersFamilyPackageService } from '@libs/modules/customers/services/customers-family-package.service';
import { EcomOrderService } from 'apps/rsa-ecom/src/modules/ecom-order/services/ecom-order.service';
import { RsaEcomService } from 'vac-nest-rsa-ecom';
import { checkWarningStockFromCart, replaceSS } from '../helper/intex';
import { RSA_ECOM_HARD_DEFAULT_SHOP_CODE } from '@shared/common/hard-code';

@Injectable()
export class OrdersFamilyPackageService {
  orderChannel: string;
  token: string;

  constructor(
    @Inject(WINSTON_MODULE_NEST_PROVIDER) private readonly logger: LoggerService,
    @Inject(REQUEST)
    private readonly req: Request,
    private readonly cartAppService: CartAppService,
    private readonly orderUtilsService: OrderUtilsService,
    private readonly promotionService: PromotionService,
    private readonly omsService: OMSService,
    private readonly paymentGWService: PaymentGatewayService,
    private readonly pimAppService: PIMAppService,
    private readonly redisService: RedisService,
    private readonly businessOrderService: BusinessOrderService,
    private readonly examinationCoreService: ExaminationCoreService,
    private readonly journeyService: JourneyService,
    private readonly regimenService: RegimenService,
    private readonly customersRuleService: CustomersRuleService,
    private readonly familyService: FamilyService,
    private readonly regimensService: RegimensService,
    private readonly ordersService: OrdersService,
    private readonly filesService: FilesService,
    private readonly customerService: CustomersService,
    private readonly orderRuleEngineService: OrderRuleEngineService,
    private readonly orderRedisService: OrderRedisService,
    private readonly camundaApiService: CamundaApiService,
    private readonly customersFamilyPackageService: CustomersFamilyPackageService,
    private readonly rsaEcomService: RsaEcomService,
    private readonly ecomOrderService: EcomOrderService,
  ) {
    this.orderChannel = (this.req.headers?.['order-channel'] as string) || '';
    this.token = (this.req.headers?.authorization as string) || (this.req.headers?.Authorization as string) || '';
  }

  // Rule validate check max injection
  private async checkMaxInjection(getCarts: GetCartLibResponse[]) {
    const checkRuleMaxInjectionDto: CheckRuleMaxInjectionDto[] = [];
    getCarts?.forEach((getCart) => {
      const listCartSelectedProduct = getCart?.listCartSelected?.filter(
        (itemCart) => itemCart?.itemType === ItemType.Product,
      );
      if (listCartSelectedProduct?.length) {
        const injectionPerson = {
          items: listCartSelectedProduct?.map((cartItem) => ({
            sku: cartItem?.itemCart,
            quantity: cartItem?.quantity,
            regimenId: cartItem?.regimenId,
          })),
          lcvId: listCartSelectedProduct?.at(0)?.lcvId,
          isShowLcvId: true,
        };
        checkRuleMaxInjectionDto.push(injectionPerson);
      }
    });

    if (checkRuleMaxInjectionDto?.length) {
      const lstRuleMaxInjections = await this.orderRuleEngineService.checkRuleMaxInjectionMultiPerson(
        checkRuleMaxInjectionDto,
      );

      lstRuleMaxInjections.forEach((lstRuleMaxInjection) => {
        const restrictMaxInjection = lstRuleMaxInjection?.items?.find((injection) => injection?.isRestrict);
        if (restrictMaxInjection) {
          throw new SystemException(
            {
              code: ErrorCode.RULE_MAX_INJECTION_DISEASE,
              message: lstRuleMaxInjection?.msg,
              details: lstRuleMaxInjection?.msg,
            },
            HttpStatus.FORBIDDEN,
          );
        }
      });
    }
  }

  async placeOrder(placeOrderDto: PlaceOrderDto) {
    const orderChannel = this.req['headers']?.['order-channel'] as string;

    // Chặn chỉ định tiêm mũi TTTP cho đơn nhóm
    this.checkRuleIndicationTTTP(placeOrderDto?.createTicketDto);

    // Noted - get cart -> giữ
    const listSessionId = placeOrderDto?.createTicketDto?.map((item) => `${placeOrderDto?.sessionId}:${item.lcvId}`);
    const listCart = await Promise.all(
      listSessionId?.map((item) => {
        const personSubId: string = item?.split(':')?.at(-1);
        return this.cartAppService.getCartRedis(
          {
            ...placeOrderDto,
          },
          {
            headers: {
              'cart-type': EnmCartType.FAMILY_PACKAGE,
              'lcv-id': personSubId,
            } as any,
          },
        );
      }),
    );

    // Noted - check cho order affiliate -> bỏ
    // Noted - xử lý cho cart preorder -> bỏ
    // Noted - check trùng sku đơn ecom -> bỏ
    // Noted - filter trùng lịch hẹn -> bỏ

    // rule chặn bán -> bỏ

    // Noted - rule loyalty -> bỏ
    // const { loyalty, checkPromotion, headerData, listCartSelected } = getCart;

    // Check có đủ mũi cho mỗi người trong gia đình không
    this.checkRuleCartItemPerPerson(listCart);

    // Check max injection
    await this.checkMaxInjection(listCart);

    // Rule chặn tạo đơn có chưa tag "Tạm hết tồn"
    await Promise.all(
      listCart?.map((cartItem) =>
        this.orderUtilsService.validateSellRestrictProducts(
          cartItem?.listCartSelected,
          ErrorCode.RSA_SKU_SELL_RESTRICT_AT_PLACE_ORDER,
          [
            placeOrderDto?.createTicketDto?.find(
              (createTicket) => cartItem?.listCartSelected?.at(0)?.lcvId === createTicket?.lcvId,
            ),
          ],
        ),
      ),
    );

    // Check rule stock
    listCart?.forEach((cart) => {
      checkWarningStockFromCart(cart, orderChannel);
    });

    const arrLCVId = placeOrderDto?.createTicketDto?.map((item) => item?.lcvId);

    const incentiveOrderRedis = await this.orderRedisService.getIncentiveOrderRedis(arrLCVId);

    // Check tổng tiền từ incentiveOrderRedis phải nhỏ hơn tổng tiền của các cart
    this.validateIncentiveOrderAmount(incentiveOrderRedis, listCart);

    // Noted - check phone -> bỏ

    // Noted - check promotion -> giữ
    const handlePromotion = async (checkPromotion: any) => {
      const checkPromotionDto: CheckPromotionResponse = parseJson(checkPromotion || '{}');
      if (!_.isEmpty(checkPromotionDto)) {
        await this.promotionService.checkPromotion({
          ...checkPromotionDto,
          sessionId: placeOrderDto.sessionId,
          orderType: placeOrderDto.orderType.toString(),
        });
      }

      // Noted - Lấy danh sách những promotion có schemaCode -> giữ
      const schemaPromotions: PromotionResponse[] = [];
      checkPromotionDto?.items?.forEach((itemPromotion) => {
        itemPromotion?.promotions?.forEach((entry) => {
          if (
            entry?.paymentMethods?.find((paymentmethod) => paymentmethod?.schemeCode) &&
            entry?.allowSelect &&
            entry?.matching &&
            entry?.applied
          ) {
            schemaPromotions.push(entry);
          }
        });
      });
      checkPromotionDto?.promotions?.forEach((entry) => {
        if (
          entry?.paymentMethods?.find((paymentmethod) => paymentmethod?.schemeCode) &&
          entry?.allowSelect &&
          entry?.matching &&
          entry?.applied
        ) {
          schemaPromotions.push(entry);
        }
      });
    };

    await Promise.all(listCart?.map((itemCart) => handlePromotion(itemCart.checkPromotion)));

    // Check promotion for all
    const [resPromotionOfCartSum, { listProduct }] = await Promise.all([
      this.cartAppService.getPromotionCartTotal({
        sessionId: placeOrderDto?.sessionId,
        shopCode: placeOrderDto?.shopCode,
        orderType: 8,
      }),
      this.pimAppService.getListProductBySkuNoRule([ITEM_VACCINE_SO1]),
    ]);

    // Call api check promotion cart sum
    await this.promotionService.checkPromotionForFamily({
      ...resPromotionOfCartSum,
      orderType: '8', // Fix Để tạm
    });
    // End check
    // Noted - tạo đơn -> giữ
    const totalBillAllCart = listCart?.reduce(
      (acc: number, cur) => acc + (cur?.calculatorPriceInfo?.totalBill || 0),
      0,
    );

    // Only create 1 payment request code for all
    const createAdjustPayment = await this.paymentGWService.createAdjustPayment({
      amount: totalBillAllCart,
      createdBy: placeOrderDto.employeeCode,
      type: TypePayment.PICK_UP,
      paymentCode: '',
      paymentSourceType: PaymentSourceType.OMS,
      sourceCode: '',
    });
    const orderCreatePaymentItemInCreateOrder = {
      paymentAmount: createAdjustPayment?.total,
      paymentCode: createAdjustPayment?.paymentCode,
      paymentDate: createAdjustPayment?.paymentDate,
      paymentStatus: createAdjustPayment?.status,
      paymentType: +createAdjustPayment?.type,
    };

    //End create 1 payment request code for all
    const createOrderDtos = await Promise.all(
      listCart?.map((itemCart) =>
        this.createUpdateOrderDto({
          placeOrderDto,
          getCart: itemCart,
          isCreatePayment: true,
          isAdjustPayment: false,
          orderCreatePaymentItemInCreateOrder,
          createAdjustPaymentInCreateOrderOrUpdate: createAdjustPayment,
          listProduct,
          incentiveOrderRedis,
        }),
      ),
    );

    const orderPromises: Promise<CreateOrderRes>[] = [];
    let isApplyPromotionSum = true;

    for (const createOrderDto of createOrderDtos) {
      const listLineNum = createOrderDto?.details?.map((item) => item?.lineNum) || [];
      const maxLineNum = Math.max(...listLineNum) + 1;

      if (isApplyPromotionSum) {
        const promotionInCartSum = resPromotionOfCartSum?.promotions?.filter((item) => !!item?.applied) || [];
        const listGift: any[] = resPromotionOfCartSum?.listGiftProduct?.map((productItem, index: number) => {
          return plainToInstance(
            ListGiftCreateOrderTransformDto,
            {
              ...productItem,
              lineNum: maxLineNum + index,
            },
            {
              excludeExtraneousValues: false,
              exposeUnsetFields: false,
            },
          );
        });

        // const listVoucher: any[] = JSONPath({
        //   path: `$..vouchers[*]`,
        //   json: promotionInCartSum,
        // }); -->check lại
        createOrderDto.details = [
          ...createOrderDto?.details,
          ...listGift,
          // ...listVoucher
        ]; //Map thêm gift,voucher vào detail
        createOrderDto.promotions = [...createOrderDto.promotions, ...promotionInCartSum]; // Map thêm promotion tổng
        isApplyPromotionSum = false;
      }

      const orderPromise = this.omsService.createOrder(createOrderDto);
      orderPromises.push(orderPromise);
    }

    const orders = await Promise.all(orderPromises);

    let voucher: VerifyVoucherResponse = null;
    let errorVoucher = null;
    // generate voucher
    const voucherKMTong = resPromotionOfCartSum?.voucherGen;
    let appliedVoucher = true;
    for (const cart of listCart) {
      const orderFind = orders?.find((e) => e?.journeyId === cart?.headerData?.journeyId);
      const { errorVoucher: err, voucher: verifyVoucher } =
        await this.orderUtilsService.generateVoucherCreateUpdateOrder(
          orderFind,
          cart,
          placeOrderDto,
          appliedVoucher ? voucherKMTong : { vouchersInValid: [], vouchersValid: [] },
          {
            headers: {
              ...this.req.headers,
              'cart-type': EnmCartType.FAMILY_PACKAGE,
              'lcv-id': cart?.listCartSelected?.at(0)?.lcvId,
            } as any,
          },
          true,
        );
      if (appliedVoucher) {
        appliedVoucher = false;
      }
      if (!voucher && verifyVoucher) {
        voucher = verifyVoucher;
      } else if (voucher && verifyVoucher) {
        voucher.validVouchers.push(...verifyVoucher.validVouchers);
        voucher.invalidVouchers.push(...verifyVoucher.invalidVouchers);
      }
      if (err) {
        errorVoucher = err;
      }
    }

    // Noted - voucher -> giữ
    const cartConfirmPromise: Promise<any>[] = [];
    const orderEcomPromise: Promise<any>[] = [];
    for (const order of orders) {
      const cart = listCart?.find((itemCart) => {
        return itemCart?.headerData?.journeyId === order?.journeyId;
      });

      const voucherFilter = voucher?.validVouchers?.filter((item) => item?.orderCode === order?.orderCode);
      const voucherClone: VerifyVoucherResponse = {
        ...voucher,
        validVouchers: voucherFilter,
      };

      cartConfirmPromise.push(
        this.orderUtilsService.addCartConfirmUtil(
          cart.headerData?.sessionId,
          order,
          cart.loyalty,
          cart.checkPromotion,
          cart?.listCartSelected,
          voucherClone,
          cart?.calculatorPriceInfo,
        ),
      );
      // set redis ticket

      const lcvId = cart?.listCartSelected?.find((e) => e.itemType === ItemType.Product)?.lcvId;
      if (placeOrderDto?.createTicketDto?.length) {
        const createTicketDto: CreateTicketDto = placeOrderDto?.createTicketDto.find((e) => e.lcvId === lcvId);
        if (createTicketDto) {
          this.redisService.set(
            `${TICKET_CREATE_KEY}:${order.orderCode}`,
            JSON.stringify(createTicketDto),
            'EX',
            getExpiredTime('day', 10),
          );
        }
      }

      // Handle FV-15634
      // Step 1: Kiểm tra xem đây có phải là đơn chuyển đổi từ đơn ecom
      const findOrderIncentiveRedis = incentiveOrderRedis?.find((item) => item?.lcvId === lcvId);
      const orderIncentiveData = findOrderIncentiveRedis?.data;
      const isOrderEcom = [...OrderChannels.RSA_ECOM, ...OrderChannels.WEB_APP].includes(
        orderIncentiveData?.orderChannel,
      );
      const isCreateOrderFromEcom = OrderChannels.RSA_ECOM.includes(this.orderChannel);
      if (isOrderEcom) {
        // Step 2: Tạo thông tin data ecom
        const orderEcomData = isCreateOrderFromEcom
          ? await this.ecomOrderService.getByOrderCode(orderIncentiveData?.orderCode)
          : await this.rsaEcomService.getEcomOrder(orderIncentiveData?.orderCode);
        !!orderEcomData &&
          orderEcomPromise.push(
            isCreateOrderFromEcom
              ? this.ecomOrderService.create({
                  ...orderEcomData,
                  orderCode: order.orderCode,
                  createdAt: undefined,
                  updatedAt: undefined,
                })
              : this.rsaEcomService.createEcomOrder({
                  ...orderEcomData,
                  orderCode: order.orderCode,
                  createdAt: undefined,
                  updatedAt: undefined,
                }),
          );
      }
      // End handle FV-15634
    }

    await Promise.all([...cartConfirmPromise, ...orderEcomPromise]);

    return { orders, voucher, errorVoucher };
  }

  async updatePlaceOrder(placeOrderDto: PlaceOrderDto) {
    const orderChannel = this.req['headers']?.['order-channel'] as string;
    const { orderCodes } = placeOrderDto;

    // Chặn chỉ định tiêm mũi TTTP cho đơn nhóm
    this.checkRuleIndicationTTTP(placeOrderDto?.createTicketDto);
    /**
     * @TODO
     *  - Lấy cart (xử lý tạo đơn)
     *  - Tạo paymentCode
     *  - Cập đơn
     *  - Huỷ cart confirm
     *  - Tạo Cart Confirm
     */
    // eslint-disable-next-line prefer-const
    const listSessionId = placeOrderDto?.createTicketDto?.map((item) => `${placeOrderDto?.sessionId}:${item.lcvId}`);
    const listCart = await Promise.all(
      listSessionId?.map((item) => {
        const personSubId: string = item?.split(':')?.at(-1);
        return this.cartAppService.getCartRedis(
          {
            ...placeOrderDto,
          },
          {
            headers: {
              'cart-type': EnmCartType.FAMILY_PACKAGE,
              'lcv-id': personSubId,
            } as any,
          },
        );
      }),
    );

    // Check có đủ mũi cho mỗi người trong gia đình không
    this.checkRuleCartItemPerPerson(listCart);

    // Check max injection
    await this.checkMaxInjection(listCart);

    // Rule chặn tạo đơn có chưa tag "Tạm hết tồn"
    await Promise.all(
      listCart?.map((cartItem) =>
        this.orderUtilsService.validateSellRestrictProducts(
          cartItem?.listCartSelected,
          ErrorCode.RSA_SKU_SELL_RESTRICT_AT_PLACE_ORDER,
          [
            placeOrderDto?.createTicketDto?.find(
              (createTicket) => cartItem?.listCartSelected?.at(0)?.lcvId === createTicket?.lcvId,
            ),
          ],
        ),
      ),
    );

    // Check rule stock
    listCart?.forEach((cart) => {
      checkWarningStockFromCart(cart, orderChannel);
    });

    const [{ orders: arrGetOrder }] = await concurrentPromise(
      this.omsService.getListOrderES({
        orderCode: orderCodes,
      }),
    );

    // Noted - xử lý đơn affiliate -> bỏ
    // Noted - xử lý cart preorder -> bỏ

    if (arrGetOrder.find((e) => e.orderStatus === OrderStatus.Cancel)) {
      const exception: IError = {
        code: ErrorCode.RSA_BLOCK_PLACE_WITH_CANCEL_STATUS,
        message: ErrorCode.getError(ErrorCode.RSA_BLOCK_PLACE_WITH_CANCEL_STATUS),
        details: ErrorCode.getError(ErrorCode.RSA_BLOCK_PLACE_WITH_CANCEL_STATUS),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.FORBIDDEN);
    }

    // Noted - check trùng đơn sku ecom -> bỏ

    // Noted - filter trùng lịch hẹn -> bỏ
    // rule chặn bán -> bỏ

    // if (!getCart?.listCartSelected?.length) {
    //   let exception: IError = {
    //     code: ErrorCode.RSA_CART_ORDER,
    //     message: ErrorCode.getError(ErrorCode.RSA_CART_ORDER),
    //     details: ErrorCode.getError(ErrorCode.RSA_CART_ORDER),
    //     validationErrors: null,
    //   };

    //   if (getOrder?.orderStatus === OrderStatus.Confirmed) {
    //     exception = {
    //       code: ErrorCode.RSA_CART_ORDER_ORDER_STATUS_CONFIRM,
    //       message: ErrorCode.getError(ErrorCode.RSA_CART_ORDER_ORDER_STATUS_CONFIRM),
    //       details: ErrorCode.getError(ErrorCode.RSA_CART_ORDER_ORDER_STATUS_CONFIRM),
    //       validationErrors: null,
    //     };
    //   }

    //   throw new SystemException(exception, HttpStatus.FORBIDDEN);
    // }

    // Noted - check promotion -> giữ
    const handlePromotion = async (checkPromotion: any) => {
      const checkPromotionDto: CheckPromotionResponse = parseJson(checkPromotion || '{}');
      if (!_.isEmpty(checkPromotionDto)) {
        await this.promotionService.checkPromotion({
          ...checkPromotionDto,
          sessionId: placeOrderDto.sessionId,
          orderType: placeOrderDto.orderType.toString(),
        });
      }
    };

    await Promise.all(listCart?.map((itemCart) => handlePromotion(itemCart.checkPromotion)));
    // Check promotion for all
    const [resPromotionOfCartSum, { listProduct }] = await Promise.all([
      this.cartAppService.getPromotionCartTotal({
        sessionId: placeOrderDto?.sessionId,
        shopCode: placeOrderDto?.shopCode,
        orderType: 8,
      }),
      this.pimAppService.getListProductBySkuNoRule([ITEM_VACCINE_SO1]),
    ]);

    // Call api check promotion cart sum
    await this.promotionService.checkPromotionForFamily({
      ...resPromotionOfCartSum,
      orderType: '8', // Fix Để tạm
    });
    // Noted - logic trả chậm -> bỏ
    // Noted - affiliate -> bỏ
    // check tien chênh lệch
    const totalBillAllCart = listCart?.reduce(
      (acc: number, cur) => acc + (cur?.calculatorPriceInfo?.totalBill || 0),
      0,
    );
    // Update payment total
    const paymentToday = listCart?.[0]?.paymentToday;
    const orderCreatePayment = arrGetOrder?.at(0)?.orderPaymentCreate || [];
    const totalDeposited = orderCreatePayment?.reduce((prev, curr) => {
      // đã thanh toán
      if (curr.paymentType === TypePayment.PICK_UP && curr?.paymentStatus === PaymentType.PAID) {
        prev += curr.paymentAmount;
      }
      if (curr.paymentType === TypePayment.DROP_OFF && curr?.paymentStatus === PaymentType.PAID) {
        prev -= curr.paymentAmount;
      }
      return prev;
    }, 0);
    const createAdjustPayment = await this.paymentGWService.createAdjustPayment({
      amount: Math.abs(totalBillAllCart + (paymentToday?.depositPrice || 0) - totalDeposited),
      createdBy: placeOrderDto.employeeCode,
      type: TypePayment.PICK_UP,
      paymentCode: arrGetOrder?.at(0)?.paymentRequestCode || '',
      paymentSourceType: PaymentSourceType.OMS,
      sourceCode: arrGetOrder?.at(0).orderCode || '',
      sourceCodes: arrGetOrder?.map((e) => e.orderCode),
    });
    // End Update payment total

    const createOrderDtos = await Promise.all(
      listCart?.map((itemCart) =>
        this.createUpdateOrderDto({
          placeOrderDto,
          getCart: itemCart,
          orders: arrGetOrder,
          isCreatePayment: false,
          isAdjustPayment: true,
          createAdjustPaymentInCreateOrderOrUpdate: createAdjustPayment,
          listProduct,
        }),
      ),
    );
    const orderPromises: Promise<CreateOrderRes>[] = [];
    const forceCancelPromises: Promise<any>[] = [];
    // Vùng Để tạm
    let isApplyPromotionSum = true;
    for (const createOrderDto of createOrderDtos) {
      if (isApplyPromotionSum) {
        const listLineNum = createOrderDto?.details?.map((item) => item?.lineNum) || [];
        const maxLineNum = Math.max(...listLineNum) + 1;
        const promotionInCartSum = resPromotionOfCartSum?.promotions?.filter((item) => !!item?.applied) || [];
        const listGift: any[] = resPromotionOfCartSum?.listGiftProduct?.map((productItem, index: number) => {
          return plainToInstance(
            ListGiftCreateOrderTransformDto,
            {
              ...productItem,
              lineNum: maxLineNum + index,
            },
            {
              excludeExtraneousValues: false,
              exposeUnsetFields: false,
            },
          );
        });

        // const listVoucher: any[] = JSONPath({
        //   path: `$..vouchers[*]`,
        //   json: promotionInCartSum,
        // }); -->check lại
        createOrderDto.details = [
          ...createOrderDto?.details,
          ...listGift,
          // ...listVoucher
        ]; //Map thêm gift,voucher vào detail
        createOrderDto.promotions = [...createOrderDto.promotions, ...promotionInCartSum]; // Map thêm promotion tổng
        isApplyPromotionSum = false;
      }
      const orderFind = arrGetOrder?.find((e) => e?.journeyId === createOrderDto?.journeyId);
      const orderPromise = this.omsService.updateOrder(orderFind.orderCode, {
        ...orderFind,
        ...createOrderDto,
        modifiedBy: placeOrderDto.employeeCode,
        modifiedByName: placeOrderDto.employeeName,
      });

      const forceCancelPromise = this.businessOrderService.forceCancel(
        false,
        listCart?.[0],
        [],
        createOrderDto?.orderPaymentCreate,
        orderFind,
        placeOrderDto?.shopCode,
        placeOrderDto.employeeCode,
      );

      forceCancelPromises.push(forceCancelPromise);
      orderPromises.push(orderPromise);
    }

    // const orderFind = arrGetOrder?.find((e) => e?.journeyId === createOrderDto?.journeyId);
    // const orderPromise = this.omsService.updateOrder(orderFind.orderCode, {
    //   ...orderFind,
    //   ...createOrderDto,
    //   modifiedBy: placeOrderDto.employeeCode,
    //   modifiedByName: placeOrderDto.employeeName,
    // });
    // orderPromises.push(orderPromise);

    // End Vùng để tạm

    // ForeCancel before update order
    await Promise.all(forceCancelPromises);

    // Update order
    const orders = await Promise.all(orderPromises);

    let voucher: VerifyVoucherResponse = null;
    let errorVoucher = null;
    // generate voucher
    const voucherKMTong = resPromotionOfCartSum?.voucherGen;
    let appliedVoucher = true;
    for (const cart of listCart) {
      const orderFind = orders?.find((e) => e?.journeyId === cart?.headerData?.journeyId);
      const { errorVoucher: err, voucher: verifyVoucher } =
        await this.orderUtilsService.generateVoucherCreateUpdateOrder(
          orderFind,
          cart,
          placeOrderDto,
          appliedVoucher ? voucherKMTong : { vouchersInValid: [], vouchersValid: [] },
          {
            headers: {
              ...this.req.headers,
              'cart-type': EnmCartType.FAMILY_PACKAGE,
              'lcv-id': cart?.listCartSelected?.at(0)?.lcvId,
            } as any,
          },
          true,
        );
      if (appliedVoucher) {
        appliedVoucher = false;
      }
      if (!voucher && verifyVoucher) {
        voucher = verifyVoucher;
      } else if (voucher && verifyVoucher) {
        voucher.validVouchers.push(...verifyVoucher.validVouchers);
        voucher.invalidVouchers.push(...verifyVoucher.invalidVouchers);
      }
      if (err) {
        errorVoucher = err;
      }
    }

    const cartConfirmPromise: Promise<any>[] = [];
    for (const order of orders) {
      const cart = listCart?.find((itemCart) => {
        return itemCart?.headerData?.journeyId === order?.journeyId;
      });

      const voucherFilter = voucher?.validVouchers?.filter((item) => item?.orderCode === order?.orderCode);
      const voucherClone: VerifyVoucherResponse = {
        ...voucher,
        validVouchers: voucherFilter,
      };

      cartConfirmPromise.push(
        this.orderUtilsService.addCartConfirmUtil(
          cart.headerData?.sessionId,
          order,
          cart.loyalty,
          cart.checkPromotion,
          cart?.listCartSelected,
          voucherClone,
          cart?.calculatorPriceInfo,
        ),
      );
      // set redis ticket

      if (placeOrderDto?.createTicketDto?.length) {
        const lcvId = cart?.listCartSelected?.find((e) => e.itemType === ItemType.Product)?.lcvId;
        const createTicketDto: CreateTicketDto = placeOrderDto?.createTicketDto.find((e) => e.lcvId === lcvId);
        if (createTicketDto) {
          this.redisService.set(
            `${TICKET_CREATE_KEY}:${order.orderCode}`,
            JSON.stringify(createTicketDto),
            'EX',
            getExpiredTime('day', 10),
          );
        }
      }
    }

    await Promise.all(cartConfirmPromise);

    return { orders, voucher, errorVoucher };
  }

  async createUpdateOrderDto({
    placeOrderDto,
    getCart,
    orders,
    isCreatePayment = false,
    isAdjustPayment = false,
    orderCreatePaymentItemInCreateOrder = null,
    createAdjustPaymentInCreateOrderOrUpdate = null,
    listProduct,
    incentiveOrderRedis,
  }: {
    placeOrderDto: PlaceOrderDto;
    getCart: GetCartLibResponse;
    orders?: GetOneOrderLibResponse[];
    isCreatePayment?: boolean;
    isAdjustPayment?: boolean;
    orderCreatePaymentItemInCreateOrder?: PartialOrderPaymentCreate;
    createAdjustPaymentInCreateOrderOrUpdate: CreateAdjustPaymentRes;
    listProduct: GetOneProductBySkuRes[];
    incentiveOrderRedis?: IncentiveOrderRedisResultDto[];
  }) {
    const { loyalty, calculatorPriceInfo, paymentToday, preOrderDepositAmount, headerData, listCartSelected } = getCart;
    const journeyId = headerData?.journeyId || '';
    const lcvId = listCartSelected?.[0]?.lcvId || '';
    const createAdjustPayment: CreateAdjustPaymentRes = createAdjustPaymentInCreateOrderOrUpdate || null;
    const orderFind = orders?.find((e) => e?.journeyId === journeyId);
    const orderCreatePayment = orderFind?.orderPaymentCreate || [];

    // Noted - xử lý đơn trả chậm -> bỏ

    // Noted - tạo payment -> giữ
    if (isCreatePayment) {
      // tạo payment mới thì + tiền cọc
      // createAdjustPayment = await this.paymentGWService.createAdjustPayment({
      //   amount: totalBillAllCart + (paymentToday?.depositPrice || 0),
      //   createdBy: placeOrderDto.employeeCode,
      //   type: TypePayment.PICK_UP,
      //   paymentCode: '',
      //   paymentSourceType: PaymentSourceType.OMS,
      //   sourceCode: '',
      // });
      orderCreatePayment.push(orderCreatePaymentItemInCreateOrder);
    } else if (isAdjustPayment) {
      // adjust thì cập nhật lại paymentAmount
      orderCreatePayment?.forEach((orderCreatePaymentEntry) => {
        if (orderCreatePaymentEntry.paymentCode === orders?.at(0)?.paymentRequestCode) {
          orderCreatePaymentEntry.paymentAmount = createAdjustPayment?.total;
        }
      });
    }

    const item = listProduct.at(0) || null;

    const incentiveOrder = incentiveOrderRedis?.find((e) => e?.lcvId === lcvId)?.data;
    // Noted - xử lý cho rsa ecom và affiliate -> bỏ
    const shopCode = placeOrderDto?.shopCode || (this.req?.headers?.['shop-code'] as string) || '';
    const createOrderDto = transformPlaceOrder(
      {
        ...placeOrderDto,
        ecomDisplay:
          orderFind?.ecomDisplay === EcomDisplay.Save
            ? EcomDisplay.Save
            : +this.req.headers?.['order-channel'] === 14 || orderFind?.ecomDisplay === EcomDisplay.AtShop
            ? EcomDisplay.AtShop
            : +EcomDisplay.AtOnline,
        phoneNumber: getCart?.headerData?.phoneNumber || placeOrderDto?.phoneNumber || '',
        orderChanel:
          incentiveOrder?.orderChannel ||
          orderFind?.orderChanel ||
          (this.req.headers?.['order-channel'] as string) ||
          '',
        shopCode,
        orderType: String(placeOrderDto.orderType),
        pointLoyalty: loyalty?.totalPoint || 0,
        details: calculatorPriceInfo?.details,
        promotions: calculatorPriceInfo?.promotions?.map((e) => ({ ...e, quantityRoot: 1 })),
        paymentRequestCode: createAdjustPayment?.paymentCode || orderFind?.paymentRequestCode || '',
        paymentRequestID: createAdjustPayment?.id || orderFind?.paymentRequestID || '',
        customerId: getCart?.headerData?.customerId || '',
        customerAddress: placeOrderDto.customerAddress || '',
        customerName: placeOrderDto.customerName || '',
        orderPaymentCreate: orderCreatePayment,
        email: getCart?.headerData?.email || '',
        companyId: '',
        tenantCode: 'FVAC', // hard code dành cho IMS
        orderAttribute: getCart?.headerData?.orderAttribute || 0,
        totalDeposit: paymentToday?.depositPrice || preOrderDepositAmount?.totalDepositAmount || 0,
        employeeCode: incentiveOrder?.createdBy
          ? incentiveOrder?.createdBy
          : orderFind
          ? orderFind?.createdBy || placeOrderDto.employeeCode
          : getCart?.headerData?.createdBy || placeOrderDto?.employeeCode || '',
        employeeName: incentiveOrder?.createdByName
          ? incentiveOrder?.createdByName
          : orderFind
          ? orderFind?.createdByName || placeOrderDto.employeeName
          : getCart?.headerData?.createdByName || placeOrderDto?.employeeName || '',
        journeyId,
      },
      {
        itemCode: item?.sku || '',
        itemName: item?.name,
        unitCode: item?.measures?.at(0)?.measureUnitId,
        unitName: item?.measures?.at(0)?.measureUnitName,
      },
    );

    const listDetailAttachments = JSONPath({
      path: `$.details[*].detailAttachments[*]`,
      json: createOrderDto,
    });

    const groupBySku = _.groupBy(listDetailAttachments, 'itemCode');
    const listSchedule = placeOrderDto?.createTicketDto.find((e) => e.lcvId === lcvId)?.schedules || [];
    const detailAttachmentNew = [];
    Object.keys(groupBySku).forEach((key) => {
      const schedules = _.sortBy(
        listSchedule?.filter((f) => f?.sku === key && f?.status === 0) || [],
        'appointmentDate',
      );
      const chulkSchedule = schedules?.slice(0, groupBySku[key]?.length);
      let index = 0;
      groupBySku[key]?.forEach((e) => {
        e.appointmentDate =
          moment(chulkSchedule?.at(index)?.appointmentDate).utcOffset(7).format() ||
          moment().utcOffset(7).add(index, 'days').format();
        e.ticketCode = placeOrderDto?.ticketCode || '';
        detailAttachmentNew.push(e);
        index++;
      });
    });

    createOrderDto.details[createOrderDto?.details?.findIndex((i) => i?.itemCode === item?.sku)].detailAttachments =
      detailAttachmentNew;

    createOrderDto.details?.forEach((entry) => {
      entry.discount = entry?.detailAttachments?.reduce((acc, cur) => acc + cur?.['discount'], 0);
    });

    createOrderDto.orderProperties =
      getCart?.calculatorPriceInfo?.preOrderTotal > 0 ? OrderProperty.Combo : OrderProperty.Retail;

    const sessionConvert = getCart?.headerData?.sessionId?.split(':');

    createOrderDto.orderIdInter = sessionConvert?.length ? `${sessionConvert[1]}:${sessionConvert[2]}` : '';

    return createOrderDto;
  }

  async updateStatusOrderMany(body: UpdateStatusOrderManyDto) {
    const { orderCodes, modifiedBy, modifiedByName } = body;

    const ticketCreateCache = await this.redisService.pipelineGet<CreateTicketDto>(
      orderCodes?.map((orderCode) => `${TICKET_CREATE_KEY}:${orderCode}`),
    );

    const ticketInfor = ticketCreateCache?.length ? ticketCreateCache : body?.ticketInfor || [];
    // remove orderCodeOld
    ticketInfor?.forEach((ticket) => {
      ticket.orderCodeOld = '';
      ticket.ticketCodeOld = '';
    });

    const promiseUpdateStatusOrderDeposit = [];

    const [{ orders: arrOrderInfo }, ...cartConfirmListOrder] = await Promise.all([
      this.omsService.getListOrderES({
        orderCode: orderCodes,
      }),
      ...orderCodes?.map((orderCode) => this.cartAppService.getCartConfirmByOrderCode(orderCode)),
    ]);
    // Handle confirm promotion
    // Step 1: confirm promotion for child cart
    await Promise.all(
      cartConfirmListOrder?.map((itemCartConfirm) => {
        const findOrderData = arrOrderInfo?.find(
          (itemOrderInfo) => itemOrderInfo?.orderCode === itemCartConfirm?.orderCode,
        );
        return this.orderUtilsService.confirmPromotion(
          itemCartConfirm?.orderCode,
          itemCartConfirm,
          findOrderData || null,
        );
      }),
    );

    const arrLCVId: string[] = _.uniq(
      JSONPath({ path: '$[*].details[*].detailAttachments[*].personIdSub', json: arrOrderInfo }),
    );
    const incentiveOrderRedis = await this.orderRedisService.getIncentiveOrderRedis(arrLCVId);
    const cancelDepositDtos: CancelDepositDto[] = [];

    // Step 2: confirm promotion for sumarize cart
    // 2.1 prepare data
    const cartConfirmDataOfFirstOrder = cartConfirmListOrder?.[0];
    const sessionId = cartConfirmDataOfFirstOrder?.sessionId;
    const sessionIdSplit = sessionId?.split(':');
    if (sessionIdSplit?.length > 3) sessionIdSplit.pop(); // Loại bỏ phần tử cuối cùng
    const sessionIdBase = sessionIdSplit.join(':');

    // Tránh oms gọi 2 api gần nhau
    // 2.2: get resPromotionOfCartSum
    const resPromotionOfCartSum = await this.cartAppService.getPromotionCartTotal({
      sessionId: sessionIdBase,
      shopCode: sessionIdSplit?.[1],
      orderType: 8,
    });
    // 2.3: confirm promotion CartSum
    await this.orderUtilsService.confirmPromotionForFamilyPackage(
      orderCodes,
      resPromotionOfCartSum,
      arrOrderInfo?.at(0),
    );
    //End Handle confirm promotion

    arrOrderInfo?.forEach((order) => {
      // Tạo step 5
      const employeeStep5 = _.orderBy(
        order?.employees?.filter((employee) => employee?.step === EmployeeStep.EmployeeUpdateOrderDeposit),
        ['modifiedDate', 'desc'],
      )?.at(0);

      const payload: UpdateOrderStatusFamilyPackageDto = {
        orderCode: order?.orderCode,
        modifiedBy: employeeStep5?.employeeCode || modifiedBy,
        modifiedByName: employeeStep5?.employeeName || modifiedByName,
        orderStatus: OrderStatus.FinishDeposit,
        ecomDisplay: EcomDisplay.AtShop,
        orderType: order.orderType,
        shopCode: this.req.headers?.['shop-code'] as string,
        paymentRequestId: order.paymentRequestCode,
        paymentStatus: PaymentOnlineStatus.Complete,
      };

      const lcvIds = _.compact(
        _.uniq(
          JSONPath({
            path: '$.details[*].detailAttachments[*].personIdSub',
            json: order,
          }),
        ),
      );

      const incentiveOrder = incentiveOrderRedis?.find((e) => e?.lcvId === lcvIds?.at(0))?.data;

      promiseUpdateStatusOrderDeposit.push(this.omsService.updateStatusOrderDepositFP(payload));

      if (incentiveOrder) {
        // Lấy thông tin cash back
        const arrOrderDetailAttachmentCode = _.compact(
          _.uniq(
            JSONPath({
              path: '$.details[*].detailAttachments[*].orderDetailAttachmentCode',
              json: order,
            }),
          ),
        );

        incentiveOrder?.orderCodes?.forEach((orderCode) => {
          cancelDepositDtos.push({
            orderCode: orderCode,
            modifiedBy: modifiedBy,
            modifiedByName: modifiedByName,
            shopCode: this.req.headers?.['shop-code'] as string,
            newOrder: {
              createdBy: order?.createdBy,
              createdByName: order?.createdByName,
              orderCode: order?.orderCode,
              createdDate: order?.createdDate,
              orderAttribute: order?.orderAttribute + '',
              orderChannel: order?.orderChanel,
              orderAttachmentCodes: arrOrderDetailAttachmentCode,
              phoneNumber: order?.phone,
            },
          });
        });
      }
    });

    // Update order status deposit cho đơn nhóm
    await concurrentPromise(...promiseUpdateStatusOrderDeposit);

    // Tạo phiếu khám
    const response = await this.handleLogicUpdateStatusOrder(
      { ...body, ticketInfor: ticketInfor },
      arrOrderInfo,
      true,
      arrOrderInfo,
    );

    // Luồng hủy đơn incentive RSA và Ecom (Có ở Ecom - api xxx-deposit-v2 update chú ý 2 bên)
    const orderChannelReq = (this.req.headers?.['order-channel'] as string) || '';
    for (const cancelDepositDto of cancelDepositDtos) {
      cancelDepositDto.orderChanelRequest = orderChannelReq;
      this.camundaApiService.cancelDeposit(cancelDepositDto);
    }

    // deleteIncentiveOrderRedis
    await this.orderRedisService.deleteIncentiveOrderRedis(arrLCVId);

    // End delete
    return response;
  }

  async handleLogicUpdateStatusOrder(
    { ticketInfor, modifiedBy, modifiedByName, journeyId, orderCodes }: UpdateStatusOrderManyDto,
    getOrder: GetOneOrderLibResponse[],
    isSuccess: boolean,
    arrOrderInfo: GetOneOrderLibResponse[],
  ) {
    if (_.isEmpty(ticketInfor)) {
      return { isSuccess, ticketInfor: [] };
    }

    const shopCode = getOrder?.at(0)?.shopCode;

    this.orderUtilsService.filterDupTicketSchedule(ticketInfor);

    // check exception with ticketInfor indication rong
    let isCheckException = false;
    ticketInfor.forEach((e) => {
      if (!e?.indications?.length && !e?.schedules?.length) {
        isCheckException = true;
      }
    });
    if (isCheckException) {
      throw new SystemException(
        {
          code: ErrorCode.RSA_EXAMINATION_TICKET_INDICATION_EMPTY,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    // Tạo phiếu khám đóng (Hoàn tất)
    const arrTicket: CreateTicketRes[] = await this.createManyTicket(ticketInfor, orderCodes, arrOrderInfo);

    // return nếu là channelEcom
    if (OrderChannels.RSA_ECOM.includes((this.req.headers?.['order-channel'] as string) || '')) {
      return { isSuccess, ticketInfor: arrTicket };
    }
    // Lọc ra những phiếu khám có mũi chỉ định để tạo phiếu khám mở
    const arrTicketHaveIndications = _.cloneDeep(arrTicket)?.filter((ticket) => ticket?.indications?.length);

    // Nếu không có chỉ định thì trả ra thông tin phiếu khám đóng
    if (!arrTicketHaveIndications?.length) return { isSuccess, ticketInfor: arrTicket };

    /**
     * @PhieuKhamMo - Luồng tạo phiếu khám mở để đi tiêm
     * Lấy thông tin persons theo lcvIds
     * Lấy rank tuổi
     * Lấy journey theo ticketCode
     * Tạo nhiều journey tiếp nhận thông tin
     * Tạo nhiều sessionId
     * Verify người FPT
     * Merge cart to customer (tạo nhiều cart)
     * Tạo nhiều step Tư vân
     * Tạo nhiều phiếu khám mở
     * Assign room nhiều tickets
     * AdjustTicket sau khi đã assign room
     * Gắn orderCode cũ cho phiếu khám mới, chỉ cho FE show
     */
    const lcvIds = arrTicketHaveIndications?.map((ticket) => ticket?.lcvId);

    // Lấy dob của personSub (Người tiêm) gắn vào merge cart
    // get DOB
    //Get journey từ phiếu cũ
    const [persons, getDOB, getJourneys] = await Promise.all([
      this.familyService.getManyByLcvId({ lcvId: lcvIds }),
      this.regimensService.getAgeRanges(),
      this.journeyService.getJourneysByListOrderCode({
        orderCodes: arrTicket.map((e) => e.orderCode),
      }),
    ]);

    // Tạo nhiều journey, session
    const genericSessions: GenericSessionLibResponse[] = await Promise.all(
      arrTicketHaveIndications?.map(() =>
        this.cartAppService.genericSession(
          { shopCode },
          {
            headers: {
              ...this.req.headers,
              'cart-type': EnmCartType.NORMAL,
              'lcv-id': '',
            } as any,
          },
        ),
      ),
    );
    const items: MultipleJourneyDto[] = [];
    arrTicketHaveIndications?.forEach((ticket, index: number) => {
      const journeyFind = getJourneys?.find((journey) => journey?.personIdSub === ticket?.lcvId);
      const payloadCreatedJourney: MultipleJourneyDto = {
        createdBy: modifiedBy || '',
        // createdByName: modifiedByName || '',
        stepIdCurrent: TypeStepId.TIEP_NHAN_THONG_TIN,
        step: [
          {
            stepId: TypeStepId.TIEP_NHAN_THONG_TIN,
            stepName: STEP_NAME[TypeStepId.TIEP_NHAN_THONG_TIN] ?? '',
            transactionNums: '',
            transactionType: TRANSACTION_TYPE['NONE'], // tạo tư vấn transaction type = 0
          },
          {
            stepId: TypeStepId.TU_VAN,
            stepName: STEP_NAME[TypeStepId.TU_VAN] ?? '',
            transactionNums: genericSessions[index]?.sessionId,
            transactionType: TRANSACTION_TYPE['CART_SESSION'], // tạo tư vấn với session cart transaction type = 1
          },
        ],
        source: this.orderChannel,
        personIdMain: journeyFind?.personIdMain,
        personIdSub: journeyFind?.personIdSub,
        custIdMain: journeyFind?.custIdMain,
        custIdSub: journeyFind?.custIdSub,
        shopCode,
        flow: FLOW_JOURNEY_ENUM.FAMILY_PACKAGE,
      };
      items.push(payloadCreatedJourney);
    });
    const dataJourneys = await this.journeyService.postCreatedMultipleJourney({ items });

    // Verify email FPTer
    const { phoneRegister, email } = await this.customersRuleService._verifyEmailFpter(lcvIds);

    // Merge nhiều cart và tạo nhiều step tư vấn
    const promiseMergeCartToCustomers = [];
    const updateTickets = [];
    dataJourneys?.forEach((journey, index) => {
      // Mapping thông tin cart để gọi merge cart
      const personSubFind = persons?.find((e) => e?.lcvId === journey?.personIdSub);
      const mappingUniKey = [personSubFind?.from, personSubFind?.to, personSubFind?.ageUnitCode].join('_');
      const isHaveDateOfBirth = mappingUniKey == '0_0_0';
      const dob = !isHaveDateOfBirth
        ? new Date(getDOB.find((e) => e.uniqkey == mappingUniKey)?.dob)
        : personSubFind?.dateOfBirth;
      const mergeCartToCustomerLibDto: MergeCartToCustomerLibDto = {
        customerId: journey?.custIdMain,
        sessionId: genericSessions[index]?.sessionId,
        phoneNumber: getOrder?.at(0)?.phone,
        phoneRegister,
        email,
        dob,
        vaccinationCode: journey?.personIdSub || '',
        createdBy: modifiedBy || '',
        createdByName: modifiedByName || '',
        journeyId: journey?.id,
      };
      promiseMergeCartToCustomers.push(
        this.cartAppService.mergeCartToCustomerForFP(mergeCartToCustomerLibDto, {
          headers: {
            ...this.req.headers,
            'cart-type': EnmCartType.NORMAL,
            'lcv-id': '',
          } as any,
        }),
      );

      const transactionNums = journey?.journeySteps?.map((itemTran) => itemTran?.transactionNums)?.filter(Boolean);
      const findGenericSessions: string =
        genericSessions?.find((itemSession) => transactionNums?.includes(itemSession?.sessionId))?.sessionId || '';
      updateTickets.push({
        lcvId: journey?.personIdSub, // key để tìm
        journeyId: journey?.id,
        sessionId: findGenericSessions,
      });
    });
    await concurrentPromise(...promiseMergeCartToCustomers);

    // Update thông tin tickets để tạo phiếu khám mở
    arrTicketHaveIndications.forEach((ticket) => {
      const updateTicketFind = updateTickets?.find((e) => e?.lcvId === ticket?.lcvId);
      ticket.ticketCode = '';
      ticket.orderCode = '';
      ticket.orderCodeOld = '';
      delete ticket.ticketVersion;
      ticket.journeyId = updateTicketFind?.journeyId;
      ticket.sessionId = updateTicketFind?.sessionId;
      ticket.paymentType = null;
      ticket.indications?.forEach((e) => {
        if (e?.orderDetailAttachmentCode) {
          e.status = 1;
        }
      });
      ticket.schedules?.forEach((e) => {
        if (e?.orderDetailAttachmentCode && e?.status === 0) {
          e.status = 1;
        }
      });
      ticket.ticketType = EnmTicketType.INDICATION;
    });

    // Tạo nhiều phiếu khám
    const arrTicketOpens = await this.examinationCoreService.createTicket(arrTicketHaveIndications as any);

    // Assign Room cho ticket
    const arrTicketCodeOpens = arrTicketOpens?.map((e) => e?.ticketCode);
    const assignRoomData: AssignRoomDtoV2 = {
      ticketCodes: arrTicketCodeOpens,
      shopCode: shopCode,
      roomType: ClinicType.PK,
      rule: EnmAssignRule.ASSIGN_EXAMINATION,
      modifiedBy: modifiedBy,
    };
    const assignRooms = await this.examinationCoreService.assignRoomV2(assignRoomData);
    const assignRoomsConvert = assignRooms?.map((item) =>
      _.pick(item, [
        'ticketCode',
        'roomType',
        'clinicId',
        'clinicName',
        'injectionClinicId',
        'injectionClinicName',
        'tableId',
        'modifiedBy',
      ]),
    );
    // Adjust ticket sau khi assign room
    const adjustTickets = await this.examinationCoreService.adjustTicketMany(assignRoomsConvert);

    // Gắn orderCode cũ cho phiếu khám mới, chỉ cho FE show
    const orderCodeOlds: string[] = [];
    adjustTickets.forEach((ticket) => {
      const ticketOldFind = arrTicket?.find((e) => e?.lcvId === ticket?.lcvId);
      ticket.orderCode = ticketOldFind?.orderCode;
      orderCodeOlds.push(ticketOldFind?.orderCode);
    });
    // Loại bỏ phiếu đã tạo phiếu tiêm
    const arrTicketCloses = arrTicket?.filter((e) => !orderCodeOlds?.includes(e?.orderCode));

    return { isSuccess, ticketInfor: [...arrTicketCloses, ...adjustTickets] };
  }

  async createManyTicket(createTicketDto: CreateTicketDto[], orderCodes: string[], orders: GetOneOrderLibResponse[]) {
    /**
     * Lấy list regimenId để lấy thông tin đường dùng và liều lượng để mapping vào indications và schedules
     * Assign room
     * Gắn thêm orderDetailAttachmentId, orderCode vào indication, schedule
     */
    for (const entry of createTicketDto) {
      entry.paymentType = PaymentType.PAID;
      entry.ticketType = EnmTicketType.NOT_INDICATION;
    }
    createTicketDto?.forEach((entry) => {
      entry.paymentType = PaymentType.PAID;
      entry.ticketType = EnmTicketType.NOT_INDICATION;
      entry.sessionId = replaceSS(entry.sessionId, entry?.lcvId);
    });

    const ticketInforPayload = await this.createAdjustTicketDtoV2(createTicketDto, orders);
    for (const entry of ticketInforPayload) {
      await this.orderUtilsService.debitPromotion(entry?.orderCode, entry?.ticketCode);
    }
    return this.examinationCoreService.createTicket(ticketInforPayload);
  }

  async createAdjustTicketDtoV2(createTicketDto: CreateTicketDto[], orderSO1: GetOneOrderLibResponse[] | null = null) {
    const detailAttachments: DetailAttachment[] = JSONPath({
      json: orderSO1,
      path: '$[*].details[*].detailAttachments[*]',
    });
    // Handle journeyId

    createTicketDto?.forEach((entry) => {
      const orderCodeFindInDetailAttachment = detailAttachments.find((e) => e.personIdSub === entry.lcvId)?.orderCode;
      const orderFind = orderSO1?.find((e) => e?.orderCode === orderCodeFindInDetailAttachment);

      entry.journeyId = orderFind?.journeyId;
    });
    // End Handle journeyId
    const arrSku: Array<string> = _.compact(
      _.uniq(
        JSONPath({
          json: createTicketDto,
          path: '$[*]..sku',
        }),
      ),
    );
    if (!arrSku?.length) {
      return createTicketDto;
    }

    const { listProduct } = await this.pimAppService.getListProductBySkuNoRule(arrSku);

    const indicationRegimenIds: string[] =
      JSONPath({
        path: '$[*].indications[*].regimenId',
        json: createTicketDto,
      }) || [];

    const scheduleRegimenIds: string[] =
      JSONPath({
        path: '$[*].schedules[*].regimenId',
        json: createTicketDto,
      }) || [];

    const regimenIds = _.uniq(scheduleRegimenIds.concat(indicationRegimenIds));
    let regimens = [];
    if (regimenIds?.length) {
      regimens = await this.regimenService.getDosagesByRegimenIds({ regimenIds });
    }

    JSONPath({
      path: '$[*]',
      json: createTicketDto,
      resultType: 'all',
    })?.forEach(({ parent, parentProperty }) => {
      if (!parent[parentProperty]) return;

      const indications: CreateTicketDto['indications'] = parent[parentProperty]?.indications;
      const schedules: CreateTicketDto['schedules'] = parent[parentProperty]?.schedules;
      const lcvInTicket = parent[parentProperty]?.lcvId;

      const orderCodeFindInDetailAttachment = detailAttachments.find((e) => e.personIdSub === lcvInTicket)?.orderCode;
      const orderFind = orderSO1?.find((e) => e?.orderCode === orderCodeFindInDetailAttachment);

      parent[parentProperty].orderCode = orderFind?.orderCode;

      schedules.forEach((scheduleEntry) => {
        // Không có so1 hoặc so1 === So1 header => update orderDetailAttachmentId
        if (orderSO1) {
          const orderAttachment = orderFind?.details?.find(
            (e) => e.itemCode === ITEM_VACCINE_SO1 && e.detailAttachments?.at(0)?.personIdSub === lcvInTicket,
          );
          const orderDetailAttachmentFind = orderAttachment?.detailAttachments?.find(
            (e) =>
              isSameDate(new Date(e['appointmentDate']), new Date(scheduleEntry.appointmentDate)) &&
              e.itemCode === scheduleEntry.sku,
          );
          if (orderDetailAttachmentFind && scheduleEntry?.status !== EnmScheduleStatus.CANCELED) {
            if (
              scheduleEntry?.status === EnmScheduleStatus.WAITING_FOR_PAYMENT ||
              !scheduleEntry?.orderDetailAttachmentCode
            ) {
              scheduleEntry.orderDetailAttachmentId = orderDetailAttachmentFind?.id;
              scheduleEntry.orderDetailAttachmentCode = orderDetailAttachmentFind?.orderDetailAttachmentCode;
            }
            // Cập nhật trạng thái schedule
            const indicationFind = indications?.find(
              (e) => e?.orderDetailAttachmentCode === orderDetailAttachmentFind?.orderDetailAttachmentCode,
            );
            if (indicationFind) {
              scheduleEntry.status = indicationFind?.status || scheduleEntry?.status;
            }
          }
        }
        const regimen = regimens?.find((regimenEntry) => regimenEntry?.id === scheduleEntry?.regimenId);
        scheduleEntry['scheduleId'] = scheduleEntry['scheduleId'] ?? null;
        scheduleEntry.injectionRoute = scheduleEntry?.injectionRoute
          ? scheduleEntry?.injectionRoute
          : regimen?.injectionRoutes?.find((injection) => injection?.isDefault)?.usage || '';
        scheduleEntry.dosage = scheduleEntry?.dosage ? scheduleEntry?.dosage : regimen?.dosages?.at(0) ?? '';
        scheduleEntry.unitCode = listProduct?.find((e) => e.sku === scheduleEntry.sku)?.isMultiDose
          ? listProduct?.find((e) => e.sku === scheduleEntry.sku)?.measures?.find((e) => e.isSellDefault)?.measureUnitId
          : scheduleEntry?.unitCode;
        scheduleEntry.unitName = listProduct?.find((e) => e.sku === scheduleEntry.sku)?.isMultiDose
          ? listProduct?.find((e) => e.sku === scheduleEntry.sku)?.measures?.find((e) => e.isSellDefault)
              ?.measureUnitName
          : scheduleEntry?.unitName;

        if (!scheduleEntry.status) {
          scheduleEntry.status = EnmScheduleStatus.WAITING_FOR_PAYMENT;
        }
        if (scheduleEntry.status === 5) {
          scheduleEntry['partialPaid'] = 5;
        }
      });

      indications.forEach((indicationEntry) => {
        // Không có so1 hoặc so1 === So1 header => update orderDetailAttachmentId
        const regimen = regimens?.find((regimenEntry) => regimenEntry?.id === indicationEntry?.regimenId);
        if (
          (!indicationEntry.orderDetailAttachmentCode ||
            indicationEntry.orderCode === parent['orderCode'] ||
            indicationEntry.status === 0 ||
            !indicationEntry.status) &&
          orderSO1
        ) {
          const orderAttachment = orderFind?.details
            ?.find((e) => e.itemCode === ITEM_VACCINE_SO1 && e.detailAttachments?.at(0)?.personIdSub === lcvInTicket)
            ?.detailAttachments.find(
              (e) => e.itemCode === indicationEntry.sku && isSameDate(new Date(e.appointmentDate), new Date()),
            );

          // Case qua ngày mapping lại odac từ Schedule
          const scheduleMappingFind = schedules?.find((e) => e?.sku === indicationEntry?.sku);

          indicationEntry.orderDetailAttachmentId =
            orderAttachment?.id ||
            indicationEntry?.orderDetailAttachmentId ||
            scheduleMappingFind?.orderDetailAttachmentId;
          indicationEntry.orderCode = indicationEntry?.orderCode;
          indicationEntry.orderDetailAttachmentCode =
            orderAttachment?.orderDetailAttachmentCode ||
            indicationEntry?.orderDetailAttachmentCode ||
            scheduleMappingFind?.orderDetailAttachmentCode;
        }
        indicationEntry.injectionRoute = indicationEntry?.injectionRoute
          ? indicationEntry?.injectionRoute
          : regimen?.injectionRoutes?.find((injection) => injection?.isDefault)?.usage || '';
        indicationEntry.dosage = indicationEntry?.dosage ? indicationEntry?.dosage : regimen?.dosages?.at(0) ?? '';

        indicationEntry.unitCode = listProduct?.find((e) => e.sku === indicationEntry.sku)?.isMultiDose
          ? listProduct?.find((e) => e.sku === indicationEntry.sku)?.measures?.find((e) => e.isSellDefault)
              ?.measureUnitId
          : indicationEntry?.unitCode;
        indicationEntry.unitName = listProduct?.find((e) => e.sku === indicationEntry.sku)?.isMultiDose
          ? listProduct?.find((e) => e.sku === indicationEntry.sku)?.measures?.find((e) => e.isSellDefault)
              ?.measureUnitName
          : indicationEntry?.unitName;
      });

      parent[parentProperty].indications = indications;
      parent[parentProperty].schedules = schedules;
    });

    return createTicketDto;
  }

  async cancelOrder(cancelOrderDto: CancelOrderDto) {
    // Step 1: get order detail
    const orderData = await this.omsService.getOneOrder(cancelOrderDto?.orderCode);

    // Step 2: check isOrderAttribute = 9 and !!orderIdInter
    const isGetListOrderSameIdInter = !!(orderData?.orderAttribute === 9 && orderData?.orderIdInter);

    // Step 3: get listOrder By order orderIdInter
    const resOrderSameIdInter = isGetListOrderSameIdInter
      ? await this.omsService.searchOrderDynamicES({
          maxResultCount: 20, //BA nguyennt81 confirm 20
          skipCount: 0,
          searchMatchPhraseFields: {
            orderIdInter: orderData?.orderIdInter,
          },
        })
      : { totalCount: 0, items: [] };

    // Step 4: chuẩn bị payload
    const payloadDelete: CancelOrderDto[] = resOrderSameIdInter?.items?.map((item) => ({
      ..._.pick(cancelOrderDto, [
        'modifiedBy',
        'modifiedByName',
        'orderCancelChannel',
        'reason',
        'reasonCancel',
        'reasonId',
        'shopCode',
      ]),
      orderID: item?.orderID,
      orderCode: item?.orderCode,
    }));

    // Step 5: verify cancel order
    const orderChannel = (this.req.headers['order-channel'] as string) || '';
    // FV-18290 - CHp phép huỷ cho trường hợp RSA ECom và đơn confirm
    if (![...OrderChannels.RSA_ECOM].includes(orderChannel) && orderData?.orderStatus === OrderStatus.Confirmed) {
      const { processedPayments: cancelOrdersVerified } = await this.paymentGWService.verifyCancelPaymentVaccine({
        paymentCodeWithShopCodes: resOrderSameIdInter?.items?.map((item) => ({
          orderCode: item.orderCode,
          shopCode: item.shopCode,
          paymentCode: item.paymentRequestCode,
          phoneNumber: item.phone,
          referenceId: '',
        })),
        fromSystem: 'BE RSA',
        cancelBy: cancelOrderDto.modifiedBy,
      });

      const ordersCannotCancel = cancelOrdersVerified?.filter(({ isVerifyCancelled }) => !isVerifyCancelled);

      if (ordersCannotCancel?.length) {
        throw new SystemException(
          {
            code: ordersCannotCancel[0].errorCode,
            message: ordersCannotCancel[0].message,
            details: ordersCannotCancel[0].message,
            validationErrors: null,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    // Step 6: Cancel các đơn
    const resCancelOrderArr = await Promise.all(
      payloadDelete?.map((item) => this.ordersService.cancelOrderFamilyPackage(item, true)),
    );

    // Step 7: deleteIncentiveOrderRedis
    const arrLCVId: string[] = _.uniq(
      JSONPath({ path: '$[*].details[*].detailAttachments[*].personIdSub', json: resOrderSameIdInter?.items }),
    );
    await this.orderRedisService.deleteIncentiveOrderRedis(arrLCVId);
    // End delete
    return resCancelOrderArr;
  }

  /**
   * @TODO Thực hiện việc tiếp tục mua hàng
   */
  async continueBuying(orderCode: string, _isEcom = false, employeeInfo?: EmployeeInfoContinueBuyingDto) {
    /**
     * @TODO
     *    - Lấy thông tin đơn hàng
     *    - Adjust cart
     *    - vac order (lịch sử tiêm của người đó)
     */

    const getOrder = await this.omsService.getOneOrder(orderCode);

    const orderChannel = (this.req.headers['order-channel'] as string) || '';
    if (
      getOrder?.orderAttribute === 9 &&
      (OrderChannels.RSA_AFFILIATE.includes(orderChannel) ||
        (OrderChannels.RSA_ECOM.includes(orderChannel) &&
          getOrder?.orderIdInter?.split(':')?.at(0) !== RSA_ECOM_HARD_DEFAULT_SHOP_CODE))
    ) {
      throw new SystemException(
        {
          code: ErrorCode.AFFILLIATE_ECOM_NOT_ALLOWED_CONTINUE_BUYING,
          message: ErrorCode.getError(ErrorCode.AFFILLIATE_ECOM_NOT_ALLOWED_CONTINUE_BUYING),
          details: ErrorCode.getError(ErrorCode.AFFILLIATE_ECOM_NOT_ALLOWED_CONTINUE_BUYING),
          validationErrors: null,
        },
        HttpStatus.FORBIDDEN,
      );
    }

    // check isOrderAttribute = 9 and !!orderIdInter
    const isGetListOrderSameIdInter = !!(getOrder?.orderAttribute === 9 && getOrder?.orderIdInter);

    // get listOrder By order orderIdInter
    const resOrderSameIdInter = isGetListOrderSameIdInter
      ? await this.omsService.searchOrderDynamicES({
          maxResultCount: 20, //BA nguyennt81 confirm 20
          skipCount: 0,
          searchMatchPhraseFields: {
            orderIdInter: getOrder?.orderIdInter,
          },
        })
      : { totalCount: 0, items: [] };

    const arrLCVId: string[] = _.uniq(
      JSONPath({ path: '$[*].details[*].detailAttachments[*].personIdSub', json: resOrderSameIdInter?.items }),
    );

    // https://reqs.fptshop.com.vn/browse/FV-10201
    // RSA nếu có phiếu khám mở thì chặn xử lý đơn đặt cọc

    // Task https://reqs.fptshop.com.vn/browse/FV-10335
    // Sub-task https://reqs.fptshop.com.vn/browse/FV-10452
    // vì check all cả đơn ecom đẩy về và đơn VT đẩy về nên xóa đi đoạn check getOrder.orderAttribute === 8 của Hải
    // nếu có phiếu và
    if (OrderChannels.RSA.includes(this.req.headers['order-channel'] as string)) {
      const resExamTicket =
        (arrLCVId?.length &&
          (await this.examinationCoreService.searchByLcsIds({
            lCVIds: arrLCVId,
          }))) ||
        [];

      const openTicket =
        resExamTicket?.length &&
        resExamTicket?.find(
          (e) =>
            [
              EnmStatusTicket.CHO_KHAM, // 1
              EnmStatusTicket.DANG_KHAM, // 2
              EnmStatusTicket.CHECKED_UP, // 3
              EnmStatusTicket.WAITING_INJECT, // 5
              EnmStatusTicket.INJECTING, // 6
            ].includes(e.status) && e.ticketType === 0,
        );
      if (openTicket) {
        const message = ErrorCode.getError(ErrorCode.RSA_BLOCK_CONTINUE_ORDER_EXIST_OPEN_TICKET).replace(
          '{ticket}',
          openTicket?.ticketCode,
        );
        throw new SystemException(
          {
            code: ErrorCode.RSA_BLOCK_CONTINUE_ORDER_EXIST_OPEN_TICKET,
            message: message,
            details: message,
            validationErrors: null,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    // const arrOrderAffiliate = await this.journeyCoreService.getOrderAffiliateInfoByLcvId(arrLCVId?.at(0));
    // this.orderUtilsService.checkRuleContinueBuyingForAffiliate(arrOrderAffiliate, getOrder);

    const listOrderExceptFirstOrder = resOrderSameIdInter?.items?.filter(
      (item) => item?.orderCode !== getOrder.orderCode,
    );
    const listPayloadAdjustOrder = [
      {
        orderCode: getOrder.orderCode,
        orderType: +getOrder.orderType,
        phoneNumber: getOrder.phone,
        shopCode: this.req.headers?.['shop-code'] as string,
      },
      ...listOrderExceptFirstOrder?.map((orderItem) => ({
        orderCode: orderItem.orderCode,
        orderType: +orderItem.orderType,
        phoneNumber: orderItem.phone,
        shopCode: this.req.headers?.['shop-code'] as string,
      })),
    ];
    const [getCart, ...restListGetCart] = await Promise.all(
      listPayloadAdjustOrder?.map((payloadItem) => this.cartAppService.adjustOrder(payloadItem)),
    );

    // Get dto tạo phiếu redis
    const listKey = resOrderSameIdInter?.items?.map((item) => `${TICKET_CREATE_KEY}:${item?.orderCode}`);
    const ticketCreateCache = await this.redisService.mget<Array<CreateTicketDto>>(listKey);

    const regimenIdCart = JSONPath({
      path: '$..schedules[*].regimenId',
      json: ticketCreateCache,
    });

    // lấy danh sách lcvids
    const lcvIds = [];
    const regimenIds = [];
    let regimens = [];
    const listGetCart = [getCart, ...restListGetCart];
    const listSelectedOfAllCart = JSONPath({
      path: '$..listCartSelected[*]',
      json: listGetCart,
    }) as ListCartSelectedLib[];
    listSelectedOfAllCart?.forEach((item) => {
      lcvIds.push(item?.lcvId);
      regimenIds.push(item?.regimenId);
    });
    const persons = (await this.familyService.getManyByLcvId({ lcvId: _.compact(_.uniq(lcvIds)) })) || [];
    // update dob to cart
    await this.cartAppService.updateDob(getCart?.headerData?.sessionId, { dob: persons?.at(0)?.dateOfBirth || '' });

    if (regimenIdCart?.length) regimenIds.push(...regimenIdCart);

    if (regimenIds.length > 0) {
      regimens = await this.regimenService.getRegimenByIds({
        regimenIds: _.compact(_.uniq(regimenIds)),
      });

      const arrSku = regimens?.map((e) => e?.vaccine?.sku);
      const { listProduct } = await this.pimAppService.getListProductBySkuNoRule(arrSku);
      regimens = regimens?.map((regimen) => {
        const product = listProduct?.find((e) => e?.sku === regimen?.vaccine?.sku);
        regimen['vaccine']['isMultiDose'] = product?.isMultiDose || false;
        return regimen;
      });
    }
    // Danh sách lịch sử tiêm bằng lcvID
    const regimenVaccineOrder = await this.regimensService.getRegimenVaccineOrderV2(arrLCVId);

    // Handle journey data
    const listJourney = await this.handleMultiJourneyData(resOrderSameIdInter, getCart, employeeInfo);
    // End Handle journey data
    const ageRanges = await this.regimenService.getAgeRanges();

    // Không bik có cần phaseId của pre-order không?????
    // let phaseId: string = '';
    // if (
    //   getOrder?.orderAttribute === 7 &&
    //   getOrder?.orderPaymentCreate?.length === 1 &&
    //   getOrder?.orderPaymentCreate?.at(0)?.paymentStatus === 1
    // ) {
    //   const arrSku: Array<string> = JSONPath({
    //     json: getOrder,
    //     path: '$.details[*]..itemCode',
    //   });

    //   const osrDepositAmountBySku = await this.osrService.getListDepositAmountBySku({
    //     listSku: arrSku,
    //   });

    //   const phase1 = osrDepositAmountBySku?.find((e) => e?.phaseId === 1);
    //   const isPhase1 = moment(moment().format('YYYY-MM-DD')).isBetween(
    //     moment(phase1?.fromDate).format('YYYY-MM-DD'),
    //     moment(phase1?.toDate).format('YYYY-MM-DD'),
    //     undefined,
    //     '[]',
    //   );

    //   if (isPhase1 && phase1) {
    //     phaseId = String(phase1?.phaseId);
    //   }

    //   const phase2 = osrDepositAmountBySku?.find((e) => e.phaseId === 2);
    //   const isPhase2 = moment(moment().format('YYYY-MM-DD')).isBetween(
    //     moment(phase2?.launchingDepositFromDate).format('YYYY-MM-DD'),
    //     moment(phase2?.launchingDepositToDate).format('YYYY-MM-DD'),
    //     undefined,
    //     '[]',
    //   );
    //   if (phase2 && isPhase2 && getOrder?.orderStatus === OrderStatus.FinishDeposit) {
    //     phaseId = String(phase2?.phaseId);
    //   }
    // }
    // if (
    //   getOrder?.orderAttribute === 8 &&
    //   [...OrderChannels.RSA_AFFILIATE].includes(getOrder?.orderChanel) &&
    //   employeeInfo?.handleEmployeeCode &&
    //   employeeInfo?.handleEmployeeName
    // ) {
    //   const payload: PayloadUpdatedStatusOrderDto = {
    //     orderCode: orderCode,
    //     modifiedBy: employeeInfo?.handleEmployeeCode,
    //     modifiedByName: employeeInfo?.handleEmployeeName,
    //     orderStatus: OrderStatus.FinishDeposit,
    //     ecomDisplay: EcomDisplay.AtShop,
    //     orderType: getOrder.orderType,
    //     shopCode: getOrder?.shopCode,
    //   };
    //   await this.omsService.updateStatusOrderDeposit(payload);
    // }

    return {
      orders: resOrderSameIdInter?.items,
      getCart: listGetCart,
      vacOrder: regimenVaccineOrder,
      persons: persons.map((p) => ({
        ...p,
        customerAge: calculateTimeDifference(p?.dateOfBirth, p?.from, p?.to, ageRanges, p?.ageUnitCode),
        pregnancy: this.customerService._checkRulePregnancy(p?.pregnancy),
      })),
      regimens,
      journeys: listJourney,
      tickets: ticketCreateCache?.filter(Boolean),
    };
  }

  // private async handleJourneyData(
  //   getOrder: GetOneOrderLibResponse,
  //   getCart: AdjustOrderLibResponse,
  //   employee_code: string,
  // ) {
  //   // call api get jounery by orderCode
  //   let journeyByOrderCode = await this.journeyService.getJourneyByOrderCode({ orderCode: getOrder?.orderCode });
  //   const cloneJourney: Array<MultipleJourneyDto> = [];
  //   Logger.log(
  //     {
  //       message: `continue-buy: ${getOrder.orderCode}`,
  //       fields: {
  //         info: `continue-buy: ${getOrder.orderCode}`,
  //         method: `GET`,
  //         url: `continue-buy: ${getOrder.orderCode}`,
  //         bodyReq: '{}',
  //         queryReq: '{}',
  //         paramsReq: '{}',
  //         headers: '{}',
  //         dataRes: JSON.stringify([
  //           getStartDate(new Date(journeyByOrderCode?.createdDate), '+00:00', 'YYYY-MM-DD'),
  //           getStartDate(new Date(), '+00:00', 'YYYY-MM-DD'),
  //         ]),
  //       },
  //     },
  //     false,
  //   );

  //   if (
  //     (!isSameDate(new Date(journeyByOrderCode?.createdDate), new Date()) &&
  //       (getOrder.orderStatus === OrderStatus.Confirmed || getOrder.orderStatus === OrderStatus.FinishDeposit)) ||
  //     ([7, 8].includes(getOrder?.orderAttribute) && getOrder?.orderStatus === OrderStatus.FinishDeposit)
  //   ) {
  //     /**
  //      * @TODO
  //      * clone journey moi
  //      * Overrite res journeyByOrderCode
  //      * cap nhat journeyId vao api moi cua bao
  //      */
  //     cloneJourney.push({
  //       ...journeyByOrderCode,
  //       step: journeyByOrderCode?.journeySteps || [],
  //       shopCode: this.req.headers?.['shop-code'] as string,
  //       createdBy: employee_code || journeyByOrderCode?.createdBy || '',
  //     });

  //     const resJourney = await this.journeyService.postCreatedMultipleJourney({ items: cloneJourney });
  //     if (resJourney?.length) {
  //       journeyByOrderCode = resJourney?.at(0);
  //     }
  //     // Update journeyId
  //     // update dob to cart
  //     await Promise.all([
  //       this.cartAppService.updateDob(getCart?.headerData?.sessionId, { journeyId: resJourney?.at(0)?.id || '' }),
  //       this.journeyService.updateJourneyId({
  //         orderInforId: getOrder?.orderID,
  //         journeyId: resJourney?.at(0)?.id,
  //         modifiedBy: employee_code || getOrder?.modifiedBy || getOrder?.createdBy || '',
  //         orderCode: getOrder.orderCode,
  //       }),
  //     ]);
  //   } else {
  //     await this.journeyService.updateShopCode(journeyByOrderCode?.id, {
  //       shopCode: this.req.headers?.['shop-code'] as string,
  //       modifiedBy: employee_code,
  //     });
  //   }

  //   if (getOrder?.urlImagePriceProduct?.length >= 0) {
  //     const key_Image = getOrder?.urlImagePriceProduct.map((img) => {
  //       return img.image;
  //     });

  //     const { links } = await this.filesService.getS3Presign({ urls: key_Image });

  //     let idx = 0;
  //     for (const item of getOrder?.urlImagePriceProduct) {
  //       item.url = links?.at(idx);
  //       ++idx;
  //     }
  //   }
  //   const personMain = await this.familyService.getPersonByPhone(getOrder?.phone);
  //   let journeyData = journeyByOrderCode;

  //   if (personMain?.at(0)?.lcvId !== journeyByOrderCode?.personIdMain) {
  //     const arrData = await this.journeyService.updateDetailMultipleJourney({
  //       modifiedBy: employee_code,
  //       details: [
  //         {
  //           custIdMain: personMain?.at(0)?.customerId,
  //           id: journeyByOrderCode?.id,
  //           personIdMain: personMain?.at(0)?.lcvId,
  //         },
  //       ],
  //     });
  //     journeyData = arrData?.at(0);
  //     await this.journeyService.updateCustomerInfo({
  //       journeyId: journeyByOrderCode?.id,
  //       customerName: personMain?.at(0)?.name || '',
  //       phone: personMain?.at(0)?.phoneNumber || '',
  //       modifiedBy: employee_code,
  //     });
  //   }
  //   return journeyData;
  // }

  private async handleMultiJourneyData(
    resOrderSameIdInter: SearchOrderESLibResponse,
    getCart: AdjustOrderLibResponse,
    employeeInfo: EmployeeInfoContinueBuyingDto,
  ) {
    const listOrderSameIdInter = resOrderSameIdInter?.items;
    // call api get jounery by orderCode
    let listJourneyByOrderCode = await this.journeyService.getJourneysByListOrderCode({
      orderCodes: listOrderSameIdInter?.map((item) => item?.orderCode),
    });
    const cloneJourney: Array<MultipleJourneyDto> = [];
    const listJourneyIdUpdateShopCode: string[] = [];
    // Log
    for (const journeyByOrderCode of listJourneyByOrderCode) {
      const listTransactionNums =
        journeyByOrderCode?.journeySteps?.map((itemJourneySteps) => itemJourneySteps?.transactionNums) || [];
      const findOrder = listOrderSameIdInter?.find((itemOrder) => listTransactionNums?.includes(itemOrder?.orderCode));
      Logger.log(
        {
          message: `continue-buy: ${findOrder?.orderCode}`,
          fields: {
            info: `continue-buy: ${findOrder?.orderCode}`,
            method: `GET`,
            url: `continue-buy: ${findOrder?.orderCode}`,
            bodyReq: '{}',
            queryReq: '{}',
            paramsReq: '{}',
            headers: '{}',
            dataRes: JSON.stringify([
              getStartDate(new Date(journeyByOrderCode?.createdDate), '+00:00', 'YYYY-MM-DD'),
              getStartDate(new Date(), '+00:00', 'YYYY-MM-DD'),
            ]),
          },
        },
        false,
      );

      if (
        (!isSameDate(new Date(journeyByOrderCode?.createdDate), new Date()) &&
          (findOrder?.orderStatus === OrderStatus.Confirmed || findOrder?.orderStatus === OrderStatus.FinishDeposit)) ||
        ([7, 8].includes(findOrder?.orderAttribute) && findOrder?.orderStatus === OrderStatus.FinishDeposit)
      ) {
        /**
         * @TODO
         * clone journey moi
         * Overrite res journeyByOrderCode
         * cap nhat journeyId vao api moi cua bao
         */
        cloneJourney.push({
          ...journeyByOrderCode,
          step: journeyByOrderCode?.journeySteps || [],
          shopCode: this.req.headers?.['shop-code'] as string,
          createdBy: employeeInfo?.handleEmployeeCode || journeyByOrderCode?.createdBy || '',
        });
      } else {
        listJourneyIdUpdateShopCode.push(journeyByOrderCode?.id);
      }
    }

    if (cloneJourney?.length) {
      const resJourney = await this.journeyService.postCreatedMultipleJourney({ items: cloneJourney });
      // Update journeyId
      // update dob to cart
      const updateDobPromise = [
        this.cartAppService.updateDob(getCart?.headerData?.sessionId, {
          journeyList: listJourneyByOrderCode?.map((itemJourney) => ({
            journeyId: itemJourney?.id,
            lcvId: itemJourney?.personIdSub,
          })),
        }),
      ];
      const updateJourneyPromises = [];
      const updateOMSDetail = [];
      for (const itemJourney of resJourney) {
        updateDobPromise.push(
          this.cartAppService.updateDob(
            getCart?.headerData?.sessionId,
            {
              journeyId: itemJourney?.id,
            },
            {
              headers: { 'cart-type': EnmCartType.FAMILY_PACKAGE, 'lcv-id': itemJourney?.personIdSub },
            },
          ),
        );
        const listTransactionNums =
          itemJourney?.journeySteps?.map((itemJourneySteps) => itemJourneySteps?.transactionNums) || [];
        const findOrder = listOrderSameIdInter?.find((itemOrder) =>
          listTransactionNums?.includes(itemOrder?.orderCode),
        );
        if (findOrder) {
          updateJourneyPromises.push(
            this.journeyService.updateJourneyId({
              orderInforId: findOrder?.orderID,
              journeyId: itemJourney?.id,
              modifiedBy: employeeInfo?.handleEmployeeCode || findOrder?.modifiedBy || findOrder?.createdBy || '',
              orderCode: findOrder?.orderCode,
            }),
          );
          updateOMSDetail.push({
            orderId: findOrder?.orderID,
            orderCode: findOrder?.orderCode,
            journeyId: itemJourney?.id,
          });
        }
      }

      const updateOMSPromises = [
        this.omsService.updateJourneyIdByOrderCode({
          modifiedBy:
            employeeInfo?.handleEmployeeCode ||
            listOrderSameIdInter.at(0)?.modifiedBy ||
            listOrderSameIdInter.at(0)?.createdBy ||
            '',
          modifiedByName:
            employeeInfo?.handleEmployeeName ||
            listOrderSameIdInter.at(0)?.modifiedByName ||
            listOrderSameIdInter.at(0)?.createdByName ||
            '',
          details: updateOMSDetail,
        }),
      ];

      await Promise.all([...updateDobPromise, ...updateJourneyPromises, ...updateOMSPromises]);
      listJourneyByOrderCode = resJourney;
    }

    if (listJourneyIdUpdateShopCode?.length) {
      await Promise.all(
        listJourneyIdUpdateShopCode?.map((item) =>
          this.journeyService.updateShopCode(item, {
            shopCode: this.req.headers?.['shop-code'] as string,
            modifiedBy: employeeInfo?.handleEmployeeCode,
          }),
        ),
      );
    }

    const handleDataCusOfJourney = async (getOrder: GetOneOrderLibResponse, journeyByOrderCode: BaseJourneyRes) => {
      if (getOrder?.urlImagePriceProduct?.length >= 0) {
        const key_Image = getOrder?.urlImagePriceProduct.map((img) => {
          return img.image;
        });

        const { links } = await this.filesService.getS3Presign({ urls: key_Image });

        let idx = 0;
        for (const item of getOrder?.urlImagePriceProduct) {
          item.url = links?.at(idx);
          ++idx;
        }
      }
      const personMain = await this.familyService.getPersonByPhone(getOrder?.phone);
      let journeyData = journeyByOrderCode;

      if (personMain?.at(0)?.lcvId !== journeyByOrderCode?.personIdMain) {
        const arrData = await this.journeyService.updateDetailMultipleJourney({
          modifiedBy: employeeInfo?.handleEmployeeCode,
          details: [
            {
              custIdMain: personMain?.at(0)?.customerId,
              id: journeyByOrderCode?.id,
              personIdMain: personMain?.at(0)?.lcvId,
            },
          ],
        });
        journeyData = arrData?.at(0);
        await this.journeyService.updateCustomerInfo({
          journeyId: journeyByOrderCode?.id,
          customerName: personMain?.at(0)?.name || '',
          phone: personMain?.at(0)?.phoneNumber || '',
          modifiedBy: employeeInfo?.handleEmployeeCode,
        });
      }
      return journeyData;
    };

    return await Promise.all(
      listJourneyByOrderCode?.map((itemJourney) => {
        const listTransactionNums =
          itemJourney?.journeySteps?.map((itemJourneySteps) => itemJourneySteps?.transactionNums) || [];
        const findOrder = listOrderSameIdInter?.find((itemOrder) =>
          listTransactionNums?.includes(itemOrder?.orderCode),
        );
        return findOrder ? handleDataCusOfJourney(findOrder, itemJourney) : null;
      }),
    );
  }

  private checkRuleCartItemPerPerson(listCart: GetCartLibResponse[]) {
    const isMissingCartItemForFB = !!listCart?.some((itemCart) => !itemCart?.listCartSelected?.length);

    if (isMissingCartItemForFB) {
      const exception: IError = {
        code: ErrorCode.RSA_MISSING_ITEMCART_FOR_FB,
        message: ErrorCode.getError(ErrorCode.RSA_MISSING_ITEMCART_FOR_FB),
        details: ErrorCode.getError(ErrorCode.RSA_MISSING_ITEMCART_FOR_FB),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.FORBIDDEN);
    }
  }

  private checkRuleIndicationTTTP(createTicketDto: CreateTicketDto[]) {
    const indicationStatus5 = JSONPath({
      path: `$[*].indications[*]`,
      json: createTicketDto,
    })?.find((indication) => indication?.status === 5);

    if (indicationStatus5) {
      const exception: IError = {
        code: ErrorCode.RSA_BLOCK_CREATE_UPDATE_ORDER_HAVE_TTTP,
        message: ErrorCode.getError(ErrorCode.RSA_BLOCK_CREATE_UPDATE_ORDER_HAVE_TTTP),
        details: ErrorCode.getError(ErrorCode.RSA_BLOCK_CREATE_UPDATE_ORDER_HAVE_TTTP),
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.FORBIDDEN);
    }
  }

  async getOrderByLcvidsAttributes(lcvIds: string[], isGetDepositedAmount: boolean = false) {
    const returnList = await this.customersFamilyPackageService.getOrderByLcvidsAttributes(lcvIds);
    // Step 3: get order in oms
    const orderCodes = returnList?.map((item) => item?.orderCode);
    const { orders: arrOrderOms } =
      isGetDepositedAmount && orderCodes?.length > 0
        ? await this.omsService.getListOrderES({
            orderCode: orderCodes,
          })
        : { orders: [] };
    // Step 4: calculateDepositedAmount
    if (isGetDepositedAmount) {
      await Promise.all(
        returnList?.map(async (item) => {
          const orderOms = arrOrderOms?.find((order) => order?.orderCode === item?.orderCode);
          if (orderOms) {
            const depositedAmount = await this.calculateDepositedAmount(orderOms);
            item['depositedAmount'] = depositedAmount;
          }
        }),
      );
    }
    return returnList;
  }

  async saveIncentiveOrderRedis(lcvIds: string[]) {
    // deleteIncentiveOrderRedis
    await this.orderRedisService.deleteIncentiveOrderRedis(lcvIds);
    // Step 1: get order by lcvIds
    const orders = await this.getOrderByLcvidsAttributes(lcvIds);
    // Step 2: get order in oms
    const orderCodes = orders?.map((item) => item?.orderCode);
    if (!orderCodes?.length) return;
    const { orders: arrOrderOms } = await this.omsService.getListOrderES({
      orderCode: orderCodes,
    });
    // Step 3: prepare data
    const body = lcvIds
      ?.map((lcvId) => {
        // Rule lấy đơn
        // List đơn của lcvId đó anh sẽ sort theo 2 tiêu chí:
        // 1. group đơn theo source với thứ tự: đơn Ecom, Aff, đơn Pre-order
        // Remove sort vì đã push data theo thứ tự ở getOrderByLcvidsAttributes rồi
        const listOrderOfLcvId = orders?.filter((item) => item?.lcvId === lcvId);

        const orderCodesOfLcvId = listOrderOfLcvId?.map((item) => item?.orderCode);
        const orderFind = listOrderOfLcvId?.[0];
        const orderOms = arrOrderOms?.find((item) => item?.orderCode === orderFind?.orderCode);
        if (!orderFind) return null;

        // Tính tổng totalPayment của tất cả đơn hàng trong lcvId
        const totalPayment =
          listOrderOfLcvId?.reduce((sum, order) => {
            return sum + (order?.totalPayment || 0);
          }, 0) || 0;

        return {
          lcvId,
          orderCode: orderFind?.orderCode,
          data: {
            lcvId,
            orderCode: orderFind?.orderCode,
            orderCodes: orderCodesOfLcvId,
            orderAttribute: orderFind?.orderAttribute,
            orderStatus: orderFind?.orderStatus,
            createdBy: orderOms?.createdBy || '',
            createdByName: orderOms?.createdByName || '',
            shopCode: orderFind?.shopCode,
            shopName: orderFind?.shopName,
            shopCodeCreate: orderFind?.shopCodeCreate,
            shopNameCreate: orderFind?.shopNameCreate,
            orderChannel: orderFind?.source || '',
            totalPayment: totalPayment,
          },
        };
      })
      ?.filter(Boolean);
    // Step 4: saveIncentiveOrderRedis
    await this.orderRedisService.saveIncentiveOrderRedis(body);
    return true;
  }

  private async calculateDepositedAmount(getOneOrder: GetOneOrderLibResponse) {
    const arrPaymentCode = _.uniq(
      _.compact(
        getOneOrder.orderPaymentCreate.map((e) => {
          if (e.paymentType === PaymentType.THU) return e.paymentCode;
        }),
      ),
    );
    const arrPaymentES: GetPaymentHistoryESLibResponse[] = await this.paymentGWService.getPaymentRedis({
      paymentCodes: arrPaymentCode,
    });

    const totalDiscountVoucher = arrPaymentES?.reduce((acc, cur) => {
      const amount = getDepositedAmountByMethods(cur?.detail, ['vouchersAll']);
      return acc + (amount !== undefined ? amount : 0);
    }, 0);

    const depositedAmount =
      arrPaymentES?.reduce((acc, cur) => {
        return acc + getDepositDetailAmountRealAmount(cur?.detail) || 0;
      }, 0) - totalDiscountVoucher;

    return depositedAmount;
  }

  async searchOrderDynamic(query: SearchOrderDynamicDto): Promise<SearchOrderESLibResponse> {
    return this.omsService.searchOrderDynamicES({
      maxResultCount: 20,
      skipCount: 0,
      searchMatchPhraseFields: {
        orderIdInter: query.orderIdInter,
      },
    });
  }

  /**
   * Kiểm tra tổng tiền từ incentiveOrderRedis phải nhỏ hơn tổng tiền của các cart
   * @param incentiveOrderRedis - Dữ liệu incentive order từ Redis
   * @param listCart - Danh sách cart
   */
  private validateIncentiveOrderAmount(incentiveOrderRedis: any[], listCart: any[]) {
    if (!incentiveOrderRedis?.length || !listCart?.length) {
      return; // Không có dữ liệu để kiểm tra
    }

    // Tính tổng totalPayment từ incentiveOrderRedis
    const totalIncentivePayment = incentiveOrderRedis.reduce((sum, item) => {
      return sum + (item?.data?.totalPayment || 0);
    }, 0);

    // Tính tổng totalBill từ listCart
    const totalCartBill = listCart.reduce((sum, cart) => {
      return sum + (cart?.calculatorPriceInfo?.totalBill || 0);
    }, 0);

    // Kiểm tra điều kiện: totalIncentivePayment phải nhỏ hơn totalCartBill
    if (totalIncentivePayment >= totalCartBill) {
      const exception: IError = {
        code: ErrorCode.RSA_INCENTIVE_AMOUNT_EXCEEDED,
        message: `Tổng tiền incentive (${totalIncentivePayment}) phải nhỏ hơn tổng tiền cart (${totalCartBill})`,
        details: {
          totalIncentivePayment,
          totalCartBill,
          difference: totalCartBill - totalIncentivePayment,
        },
        validationErrors: null,
      };
      throw new SystemException(exception, HttpStatus.BAD_REQUEST);
    }
  }
}
