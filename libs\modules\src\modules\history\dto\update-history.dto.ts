import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class HistoryItemUpdateDto {
  @ApiProperty()
  @Expose()
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsNotEmpty()
  regimenId: string;
}

export class UpdateHistoryDobDto {
  @ApiProperty({ isArray: true, type: HistoryItemUpdateDto })
  @Expose()
  @IsOptional()
  detailHistory: HistoryItemUpdateDto[];

  @ApiProperty({ isArray: true, type: HistoryItemUpdateDto })
  @Expose()
  @IsOptional()
  detailSchedule?: HistoryItemUpdateDto[];
}
