# API Verify History

## <PERSON><PERSON> tả

API kiểm tra lịch sử tiêm không phù hợp với độ tuổi và đưa ra gợi ý phác đồ thay thế phù hợp.

## Thông tin API

- **Endpoint**: `GET /history/verify-history`
- **Method**: GET
- **Authentication**: <PERSON><PERSON> required
- **Content-Type**: application/json

## Request Parameters

### Query Parameters

| Tham số    | Kiểu   | Bắt buộc | Mô tả                                  |
| ---------- | ------ | -------- | -------------------------------------- |
| `personId` | string | ✓        | ID của người cần kiểm tra lịch sử tiêm |
| `lcvId`    | string | ✓        | ID của trung tâm y tế                  |

### Ví dụ Request

```http
GET /history/verify-history?personId=123456&lcvId=789012
Authorization: Bearer <your-token>
```

## Response

### Success Response (200 OK)

Trả về danh sách lịch sử tiêm có xung đột với độ tuổi và các phác đồ gợi ý thay thế.

```json
[
  {
    "id": "string",
    "personId": "string",
    "regimenId": "string",
    "sku": "string",
    "vaccinatedDate": "2024-01-15T00:00:00.000Z",
    "injection": 1,
    "sourceId": 2,
    "listSuggestRegimen": [
      {
        "id": "string",
        "name": "string",
        "ageFromValue": 2,
        "ageFromUnitCode": 2,
        "ageToValue": 24,
        "ageToUnitCode": 2,
        "vaccine": {
          "sku": "string",
          "name": "string"
        },
        "details": [
          {
            "order": 1,
            "regimenRules": []
          }
        ]
      }
    ],
    "listSchedule": [
      {
        "id": "string",
        "regimenId": "string",
        "appointmentDate": "2024-02-15T00:00:00.000Z",
        "injection": 2
      }
    ],
    "currentRegimen": {
      "id": "string",
      "name": "string",
      "ageFromValue": 0,
      "ageFromUnitCode": 0,
      "ageToValue": 6,
      "ageToUnitCode": 2
    },
    "customerAge": {
      "from": 15,
      "to": 15,
      "ageUnitCode": 2
    }
  }
]
```

### Error Response (400 Bad Request)

```json
{
  "statusCode": 400,
  "message": "Trả lỗi khi đầu vào bị sai",
  "error": "Bad Request"
}
```

## Response Fields

### Các trường chính trong response:

| Trường               | Kiểu   | Mô tả                                            |
| -------------------- | ------ | ------------------------------------------------ |
| `id`                 | string | ID của bản ghi lịch sử tiêm                      |
| `personId`           | string | ID của người được tiêm                           |
| `regimenId`          | string | ID của phác đồ hiện tại (có xung đột)            |
| `sku`                | string | Mã SKU của vaccine                               |
| `vaccinatedDate`     | string | Ngày tiêm vaccine                                |
| `injection`          | number | Mũi tiêm thứ mấy                                 |
| `sourceId`           | number | Nguồn dữ liệu (2 = TCQG)                         |
| `listSuggestRegimen` | array  | Danh sách phác đồ gợi ý thay thế                 |
| `listSchedule`       | array  | Danh sách lịch hẹn liên quan                     |
| `currentRegimen`     | object | Thông tin phác đồ hiện tại                       |
| `customerAge`        | object | Thông tin tuổi của khách hàng tại thời điểm tiêm |

### Cấu trúc `listSuggestRegimen`:

- Được sắp xếp theo thứ tự ưu tiên: `ageUnitCode` → `from` → `isDefault` → `to`
- Chỉ chứa các phác đồ phù hợp với độ tuổi tại thời điểm tiêm
- Loại bỏ phác đồ hiện tại khỏi danh sách gợi ý

### Mã `ageUnitCode`:

- `0`: DAYS (ngày)
- `1`: WEEK (tuần)
- `2`: MONTHS (tháng)
- `3`: AGES (năm)

## Sequence Diagram

```mermaid
sequenceDiagram
    participant Client
    participant HistoryController
    participant HistoryService
    participant FamilyService
    participant VacHistoryService
    participant RegimenService
    participant ScheduleService
    participant RegimenRule as RegimenRule Functions

    Client->>HistoryController: GET /history/verify-history?personId=xxx&lcvId=yyy
    HistoryController->>HistoryService: verifyHistory(payload)

    Note over HistoryService: Extract personId, lcvId from payload

    HistoryService->>FamilyService: getPersonByLcvId(lcvId)
    FamilyService-->>HistoryService: person info (dateOfBirth, etc.)

    HistoryService->>VacHistoryService: getByPerson({personId})
    VacHistoryService-->>HistoryService: history[]

    Note over HistoryService: Filter history by sourceId === TCQG (2)

    alt No history found
        HistoryService-->>HistoryController: return []
        HistoryController-->>Client: []
    end

    Note over HistoryService: Extract unique regimenIds from history

    HistoryService->>RegimenService: getRegimenByIds({regimenIds})
    RegimenService-->>HistoryService: regimens[]

    Note over HistoryService: Filter history with age conflicts

    loop For each history item
        HistoryService->>RegimenRule: checkRuleAge(vaccinatedDate, regimen, injection, dateOfBirth)
        RegimenRule-->>HistoryService: boolean (true if conflict)
    end

    alt No conflicts found
        HistoryService-->>HistoryController: return []
        HistoryController-->>Client: []
    end

    Note over HistoryService: Extract unique SKUs from conflicted history

    HistoryService->>RegimenService: getRegimenByListSku({skus})
    RegimenService-->>HistoryService: {data: regimens[]}

    Note over HistoryService: Filter other regimens (exclude conflicted ones)

    HistoryService->>RegimenService: getRegimenByIds({regimenIds: otherRegimenIds})
    RegimenService-->>HistoryService: otherRegimens[]

    HistoryService->>ScheduleService: getListScheduleByPerson({personId, lcvId})
    ScheduleService-->>HistoryService: listSchedule

    HistoryService->>RegimenService: getAgeRanges()
    RegimenService-->>HistoryService: ageRanges[]

    Note over HistoryService: Process each conflicted history item

    loop For each conflicted history item
        Note over HistoryService: Find suitable regimens by SKU

        loop For each potential regimen
            HistoryService->>RegimenRule: checkRuleAge(vaccinatedDate, regimen, injection, dateOfBirth)
            RegimenRule-->>HistoryService: boolean (false if suitable)
        end

        HistoryService->>RegimenRule: sortSuggestRegimenByAge(suitableRegimens)
        RegimenRule-->>HistoryService: sortedRegimens[]

        Note over HistoryService: Add listSuggestRegimen to item
        Note over HistoryService: Filter and add listSchedule
        Note over HistoryService: Add currentRegimen info
        Note over HistoryService: Calculate customerAge at vaccination time
    end

    HistoryService-->>HistoryController: VerifyHistoryRes[]
    HistoryController-->>Client: JSON Response with conflicted history and suggestions
```

## Logic xử lý

1. **Lấy thông tin người dùng**: Tìm thông tin person theo `lcvId`
2. **Lấy lịch sử tiêm**: Lọc lịch sử từ nguồn TCQG (`sourceId = 2`)
3. **Kiểm tra xung đột tuổi**: Sử dụng function `checkRuleAge()` để tìm các mũi tiêm không phù hợp với độ tuổi
4. **Tìm phác đồ thay thế**:
   - Lấy các phác đồ khác cùng SKU
   - Lọc ra những phác đồ phù hợp với độ tuổi
   - Sắp xếp theo thứ tự ưu tiên bằng `sortSuggestRegimenByAge()`
5. **Bổ sung thông tin**: Thêm lịch hẹn, phác đồ hiện tại và tuổi khách hàng

## Use Cases

### 1. Kiểm tra xung đột tuổi

- Phát hiện các mũi tiêm được thực hiện khi chưa đủ tuổi hoặc quá tuổi
- Đưa ra cảnh báo cho nhân viên y tế

### 2. Gợi ý phác đồ thay thế

- Đề xuất các phác đồ phù hợp hơn với độ tuổi
- Hỗ trợ quyết định chuyển đổi phác đồ

### 3. Quản lý lịch hẹn

- Hiển thị các lịch hẹn liên quan đến phác đồ có xung đột
- Hỗ trợ điều chỉnh lịch hẹn tiếp theo

## Lưu ý

- API chỉ kiểm tra lịch sử từ nguồn TCQG (`sourceId = 2`)
- Nếu không có lịch sử hoặc không có xung đột, trả về mảng rỗng `[]`
- Danh sách gợi ý được sắp xếp theo độ ưu tiên từ cao đến thấp
- Tuổi được tính tại thời điểm tiêm, không phải thời điểm hiện tại
